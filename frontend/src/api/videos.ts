import apiClient from './client';
import { 
  VideoWithDetails, 
  CreateVideoRequest, 
  UpdateVideoRequest, 
  VideoStats,
  ApiResponse,
  PaginatedResponse 
} from '../types';

export const videosApi = {
  // Get all videos with pagination and filtering
  getVideos: async (params: {
    page?: number;
    limit?: number;
    category_id?: number;
    status?: string;
    user_id?: number;
  } = {}): Promise<PaginatedResponse<VideoWithDetails>> => {
    const response = await apiClient.get<PaginatedResponse<VideoWithDetails>>('/videos', { params });
    return response.data;
  },

  // Get video by ID
  getVideo: async (id: number): Promise<VideoWithDetails> => {
    const response = await apiClient.get<ApiResponse<VideoWithDetails>>(`/videos/${id}`);
    return response.data.data;
  },

  // Create new video
  createVideo: async (data: CreateVideoRequest): Promise<VideoWithDetails> => {
    const response = await apiClient.post<ApiResponse<VideoWithDetails>>('/videos', data);
    return response.data.data;
  },

  // Upload video file
  uploadVideo: async (id: number, file: File): Promise<void> => {
    const formData = new FormData();
    formData.append('video', file);
    
    await apiClient.post(`/videos/${id}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Update video
  updateVideo: async (id: number, data: UpdateVideoRequest): Promise<VideoWithDetails> => {
    const response = await apiClient.put<ApiResponse<VideoWithDetails>>(`/videos/${id}`, data);
    return response.data.data;
  },

  // Delete video
  deleteVideo: async (id: number): Promise<void> => {
    await apiClient.delete(`/videos/${id}`);
  },

  // Increment view count
  incrementViews: async (id: number): Promise<void> => {
    await apiClient.post(`/videos/${id}/view`);
  },

  // Get video statistics
  getStats: async (): Promise<VideoStats> => {
    const response = await apiClient.get<ApiResponse<VideoStats>>('/videos/stats');
    return response.data.data;
  },
};
