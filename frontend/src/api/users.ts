import apiClient from './client';
import { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest,
  ApiResponse,
  PaginatedResponse 
} from '../types';

export const usersApi = {
  // Get all users with pagination
  getUsers: async (params: {
    page?: number;
    limit?: number;
  } = {}): Promise<PaginatedResponse<User>> => {
    const response = await apiClient.get<PaginatedResponse<User>>('/users', { params });
    return response.data;
  },

  // Get user by ID
  getUser: async (id: number): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>(`/users/${id}`);
    return response.data.data;
  },

  // Create new user (admin only)
  createUser: async (data: CreateUserRequest): Promise<User> => {
    const response = await apiClient.post<ApiResponse<User>>('/admin/users', data);
    return response.data.data;
  },

  // Update user
  updateUser: async (id: number, data: UpdateUserRequest): Promise<User> => {
    const response = await apiClient.put<ApiResponse<User>>(`/users/${id}`, data);
    return response.data.data;
  },

  // Delete user (admin only)
  deleteUser: async (id: number): Promise<void> => {
    await apiClient.delete(`/users/${id}`);
  },

  // Get user statistics
  getStats: async (): Promise<{ total_users: number; active_users: number }> => {
    const response = await apiClient.get<ApiResponse<{ total_users: number; active_users: number }>>('/users/stats');
    return response.data.data;
  },
};
