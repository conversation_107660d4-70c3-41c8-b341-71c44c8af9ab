import React, { useEffect, useRef } from 'react';
import { VideoWithDetails } from '../../types';

interface VideoPlayerModalProps {
  video: VideoWithDetails;
  isOpen: boolean;
  onClose: () => void;
}

const VideoPlayerModal: React.FC<VideoPlayerModalProps> = ({ video, isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen && videoRef.current) {
      videoRef.current.focus();
    }
  }, [isOpen]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === modalRef.current) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const videoUrl = video.file_path ? `http://localhost:8080/${video.file_path}` : '';

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      onClick={handleOverlayClick}
    >
      <div className="relative w-full max-w-6xl mx-4">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute -top-12 right-0 text-white hover:text-gray-300 text-2xl z-10"
        >
          <i className="fas fa-times"></i>
        </button>

        {/* Video Container */}
        <div className="relative bg-black rounded-lg overflow-hidden" style={{ paddingBottom: '56.25%', height: 0 }}>
          {videoUrl ? (
            <video
              ref={videoRef}
              className="absolute top-0 left-0 w-full h-full"
              controls
              autoPlay
              src={videoUrl}
            >
              Your browser does not support the video tag.
            </video>
          ) : (
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-white">
              <div className="text-center">
                <i className="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>Video file not available</p>
              </div>
            </div>
          )}
        </div>

        {/* Video Info */}
        <div className="mt-4 text-white">
          <h2 className="text-xl font-bold mb-2">{video.title}</h2>
          {video.description && (
            <p className="text-gray-300 text-sm">{video.description}</p>
          )}
          <div className="flex items-center mt-2 text-sm text-gray-400">
            <span className="mr-4">
              <i className="fas fa-eye mr-1"></i>
              {video.views} views
            </span>
            <span className="mr-4">
              <i className="fas fa-tag mr-1"></i>
              {video.category?.name || 'Uncategorized'}
            </span>
            <span>
              <i className="fas fa-calendar mr-1"></i>
              {new Date(video.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayerModal;
