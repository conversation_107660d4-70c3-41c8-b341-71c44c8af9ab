import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { VideoWithDetails } from '../../types';
import { RootState } from '../../store';
import { deleteVideo, updateVideo } from '../../store/videosSlice';
import { incrementViews } from '../../store/videosSlice';

interface VideoCardProps {
  video: VideoWithDetails;
  onPlay: (video: VideoWithDetails) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, onPlay }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const [showMenu, setShowMenu] = useState(false);

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) return `${minutes}m ${remainingSeconds}s`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
  };

  const formatViews = (views: number): string => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'published': return 'text-green-600';
      case 'processing': return 'text-yellow-600';
      case 'private': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const handlePlay = () => {
    dispatch(incrementViews(video.id));
    onPlay(video);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this video?')) {
      dispatch(deleteVideo(video.id));
    }
  };

  const handleStatusChange = (newStatus: string) => {
    dispatch(updateVideo({ id: video.id, data: { status: newStatus } }));
    setShowMenu(false);
  };

  const canEdit = user?.role === 'admin' || user?.id === video.user_id;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-xl">
      {/* Video Thumbnail */}
      <div className="relative group cursor-pointer" onClick={handlePlay}>
        {video.thumbnail ? (
          <img
            src={video.thumbnail}
            alt={video.title}
            className="w-full h-40 object-cover"
          />
        ) : (
          <div className="w-full h-40 bg-gray-200 flex items-center justify-center">
            <i className="fas fa-play text-4xl text-gray-400"></i>
          </div>
        )}
        
        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
              <i className="fas fa-play text-2xl text-gray-800 ml-1"></i>
            </div>
          </div>
        </div>

        {/* Duration Badge */}
        {video.duration > 0 && (
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {formatDuration(video.duration)}
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 flex-1">
            {video.title}
          </h3>
          
          {canEdit && (
            <div className="relative ml-2">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <i className="fas fa-ellipsis-v"></i>
              </button>
              
              {showMenu && (
                <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border">
                  <div className="py-1">
                    <button
                      onClick={() => handleStatusChange('published')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-eye mr-2"></i>
                      Publish
                    </button>
                    <button
                      onClick={() => handleStatusChange('private')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-eye-slash mr-2"></i>
                      Make Private
                    </button>
                    <button
                      onClick={handleDelete}
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      <i className="fas fa-trash mr-2"></i>
                      Delete
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
          <span className="flex items-center">
            <i className="fas fa-tag mr-1"></i>
            {video.category?.name || 'Uncategorized'}
          </span>
          <span className={`font-medium ${getStatusColor(video.status)}`}>
            {video.status.charAt(0).toUpperCase() + video.status.slice(1)}
          </span>
        </div>

        <div className="flex items-center justify-between text-xs text-gray-500">
          <span className="flex items-center">
            <i className="fas fa-eye mr-1"></i>
            {formatViews(video.views)} views
          </span>
          <span>
            {new Date(video.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
