import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Category, CategoryWithStats, CreateCategoryRequest, UpdateCategoryRequest } from '../types';
import { categoriesApi } from '../api/categories';

interface CategoriesState {
  categories: CategoryWithStats[];
  activeCategories: Category[];
  currentCategory: CategoryWithStats | null;
  loading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    limit: number;
  } | null;
}

const initialState: CategoriesState = {
  categories: [],
  activeCategories: [],
  currentCategory: null,
  loading: false,
  error: null,
  pagination: null,
};

// Async thunks
export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async (params: {
    page?: number;
    limit?: number;
    include_stats?: boolean;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await categoriesApi.getCategories(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch categories');
    }
  }
);

export const fetchActiveCategories = createAsyncThunk(
  'categories/fetchActiveCategories',
  async (_, { rejectWithValue }) => {
    try {
      const categories = await categoriesApi.getActiveCategories();
      return categories;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch active categories');
    }
  }
);

export const fetchCategory = createAsyncThunk(
  'categories/fetchCategory',
  async ({ id, includeStats = false }: { id: number; includeStats?: boolean }, { rejectWithValue }) => {
    try {
      const category = await categoriesApi.getCategory(id, includeStats);
      return category;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch category');
    }
  }
);

export const createCategory = createAsyncThunk(
  'categories/createCategory',
  async (data: CreateCategoryRequest, { rejectWithValue }) => {
    try {
      const category = await categoriesApi.createCategory(data);
      return category;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create category');
    }
  }
);

export const updateCategory = createAsyncThunk(
  'categories/updateCategory',
  async ({ id, data }: { id: number; data: UpdateCategoryRequest }, { rejectWithValue }) => {
    try {
      const category = await categoriesApi.updateCategory(id, data);
      return category;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update category');
    }
  }
);

export const deleteCategory = createAsyncThunk(
  'categories/deleteCategory',
  async (id: number, { rejectWithValue }) => {
    try {
      await categoriesApi.deleteCategory(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete category');
    }
  }
);

// Categories slice
const categoriesSlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentCategory: (state) => {
      state.currentCategory = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.data.categories || action.payload.data;
        state.pagination = action.payload.data.pagination || null;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch active categories
      .addCase(fetchActiveCategories.fulfilled, (state, action) => {
        state.activeCategories = action.payload;
      })
      // Fetch single category
      .addCase(fetchCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCategory = action.payload;
      })
      .addCase(fetchCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create category
      .addCase(createCategory.fulfilled, (state, action) => {
        const categoryWithStats: CategoryWithStats = {
          ...action.payload,
          video_count: 0,
        };
        state.categories.unshift(categoryWithStats);
        state.activeCategories.unshift(action.payload);
      })
      // Update category
      .addCase(updateCategory.fulfilled, (state, action) => {
        const index = state.categories.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.categories[index] = { ...state.categories[index], ...action.payload };
        }
        
        const activeIndex = state.activeCategories.findIndex(c => c.id === action.payload.id);
        if (activeIndex !== -1) {
          state.activeCategories[activeIndex] = action.payload;
        }
        
        if (state.currentCategory && state.currentCategory.id === action.payload.id) {
          state.currentCategory = { ...state.currentCategory, ...action.payload };
        }
      })
      // Delete category
      .addCase(deleteCategory.fulfilled, (state, action) => {
        state.categories = state.categories.filter(c => c.id !== action.payload);
        state.activeCategories = state.activeCategories.filter(c => c.id !== action.payload);
        if (state.currentCategory && state.currentCategory.id === action.payload) {
          state.currentCategory = null;
        }
      });
  },
});

export const { clearError, clearCurrentCategory } = categoriesSlice.actions;
export default categoriesSlice.reducer;
