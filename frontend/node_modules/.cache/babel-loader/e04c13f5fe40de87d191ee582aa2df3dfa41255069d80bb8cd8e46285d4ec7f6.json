{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Settings/Settings.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-800 mb-6\",\n      children: \"Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-4\",\n          children: \"General Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Site Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"Vortex\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Site Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 3,\n              defaultValue: \"A powerful video management system\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Max Upload Size (MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              defaultValue: \"500\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n          children: \"Save General Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-4\",\n          children: \"Storage Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Storage Path\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"./uploads\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Auto-generate Thumbnails\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                defaultChecked: true,\n                className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-700\",\n                children: \"Automatically generate video thumbnails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Storage Usage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-200 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-indigo-600 h-2 rounded-full\",\n                style: {\n                  width: '45%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mt-1\",\n              children: \"452 GB of 1 TB used\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n          children: \"Save Storage Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-4\",\n          children: \"Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"JWT Secret Key\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              defaultValue: \"your-secret-key\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Session Timeout (hours)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              defaultValue: \"24\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Require Email Verification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-700\",\n                children: \"Require users to verify their email address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n          children: \"Save Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-4\",\n          children: \"Backup & Maintenance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Auto Backup\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                defaultChecked: true,\n                className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-700\",\n                children: \"Enable automatic daily backups\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Last Backup\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"July 31, 2025 at 2:00 AM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-download mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), \"Create Backup\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-broom mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), \"Clean Temp Files\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Settings", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "defaultValue", "rows", "defaultChecked", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Settings/Settings.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Settings: React.FC = () => {\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold text-gray-800 mb-6\">Settings</h1>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* General Settings */}\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">General Settings</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Site Name\n              </label>\n              <input\n                type=\"text\"\n                defaultValue=\"Vortex\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Site Description\n              </label>\n              <textarea\n                rows={3}\n                defaultValue=\"A powerful video management system\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Max Upload Size (MB)\n              </label>\n              <input\n                type=\"number\"\n                defaultValue=\"500\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n          </div>\n          \n          <button className=\"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\">\n            Save General Settings\n          </button>\n        </div>\n\n        {/* Storage Settings */}\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Storage Settings</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Storage Path\n              </label>\n              <input\n                type=\"text\"\n                defaultValue=\"./uploads\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Auto-generate Thumbnails\n              </label>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  defaultChecked\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">\n                  Automatically generate video thumbnails\n                </span>\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Storage Usage\n              </label>\n              <div className=\"bg-gray-200 rounded-full h-2\">\n                <div className=\"bg-indigo-600 h-2 rounded-full\" style={{ width: '45%' }}></div>\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">452 GB of 1 TB used</p>\n            </div>\n          </div>\n          \n          <button className=\"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\">\n            Save Storage Settings\n          </button>\n        </div>\n\n        {/* Security Settings */}\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Security Settings</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                JWT Secret Key\n              </label>\n              <input\n                type=\"password\"\n                defaultValue=\"your-secret-key\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Session Timeout (hours)\n              </label>\n              <input\n                type=\"number\"\n                defaultValue=\"24\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Require Email Verification\n              </label>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">\n                  Require users to verify their email address\n                </span>\n              </div>\n            </div>\n          </div>\n          \n          <button className=\"mt-6 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\">\n            Save Security Settings\n          </button>\n        </div>\n\n        {/* Backup Settings */}\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Backup & Maintenance</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Auto Backup\n              </label>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  defaultChecked\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">\n                  Enable automatic daily backups\n                </span>\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Last Backup\n              </label>\n              <p className=\"text-sm text-gray-600\">July 31, 2025 at 2:00 AM</p>\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\">\n                <i className=\"fas fa-download mr-2\"></i>\n                Create Backup\n              </button>\n              <button className=\"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700\">\n                <i className=\"fas fa-broom mr-2\"></i>\n                Clean Temp Files\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA;MAAIG,SAAS,EAAC,uCAAuC;MAAAD,QAAA,EAAC;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEnEP,OAAA;MAAKG,SAAS,EAAC,uCAAuC;MAAAD,QAAA,gBAEpDF,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDF,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAAD,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE9EP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEQ,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,QAAQ;cACrBN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEU,IAAI,EAAE,CAAE;cACRD,YAAY,EAAC,oCAAoC;cACjDN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEQ,IAAI,EAAC,QAAQ;cACbC,YAAY,EAAC,KAAK;cAClBN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAQG,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAE3F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNP,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDF,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAAD,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE9EP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEQ,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,WAAW;cACxBN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCF,OAAA;gBACEQ,IAAI,EAAC,UAAU;gBACfG,cAAc;gBACdR,SAAS,EAAC;cAAuE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACFP,OAAA;gBAAMG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cAAKG,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3CF,OAAA;gBAAKG,SAAS,EAAC,gCAAgC;gBAACS,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAM;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACNP,OAAA;cAAGG,SAAS,EAAC,4BAA4B;cAAAD,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAQG,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAE3F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNP,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDF,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAAD,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE/EP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEQ,IAAI,EAAC,UAAU;cACfC,YAAY,EAAC,iBAAiB;cAC9BN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cACEQ,IAAI,EAAC,QAAQ;cACbC,YAAY,EAAC,IAAI;cACjBN,SAAS,EAAC;YAA0G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCF,OAAA;gBACEQ,IAAI,EAAC,UAAU;gBACfL,SAAS,EAAC;cAAuE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACFP,OAAA;gBAAMG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAQG,SAAS,EAAC,wEAAwE;UAAAD,QAAA,EAAC;QAE3F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNP,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAD,QAAA,gBAChDF,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAAD,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAElFP,OAAA;UAAKG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBF,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCF,OAAA;gBACEQ,IAAI,EAAC,UAAU;gBACfG,cAAc;gBACdR,SAAS,EAAC;cAAuE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACFP,OAAA;gBAAMG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENP,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAOG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRP,OAAA;cAAGG,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENP,OAAA;YAAKG,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC7BF,OAAA;cAAQG,SAAS,EAAC,iEAAiE;cAAAD,QAAA,gBACjFF,OAAA;gBAAGG,SAAS,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTP,OAAA;cAAQG,SAAS,EAAC,mEAAmE;cAAAD,QAAA,gBACnFF,OAAA;gBAAGG,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA7LIb,QAAkB;AA+LxB,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}