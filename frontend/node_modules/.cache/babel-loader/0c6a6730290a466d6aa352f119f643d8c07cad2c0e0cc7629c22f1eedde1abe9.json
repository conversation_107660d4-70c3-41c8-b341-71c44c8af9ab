{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Common/Pagination.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Pagination = ({\n  currentPage,\n  totalPages,\n  onPageChange\n}) => {\n  const getPageNumbers = () => {\n    const pages = [];\n    const maxVisiblePages = 5;\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  };\n  if (totalPages <= 1) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center space-x-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(currentPage - 1),\n      disabled: currentPage === 1,\n      className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-chevron-left\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), getPageNumbers()[0] > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(1),\n        className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n        children: \"1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this), getPageNumbers()[0] > 2 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-2 text-sm text-gray-500\",\n        children: \"...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true), getPageNumbers().map(page => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(page),\n      className: `px-3 py-2 text-sm font-medium rounded-lg ${page === currentPage ? 'text-white bg-indigo-600 border border-indigo-600' : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'}`,\n      children: page\n    }, page, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 9\n    }, this)), getPageNumbers()[getPageNumbers().length - 1] < totalPages && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [getPageNumbers()[getPageNumbers().length - 1] < totalPages - 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-2 text-sm text-gray-500\",\n        children: \"...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(totalPages),\n        className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n        children: totalPages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => onPageChange(currentPage + 1),\n      disabled: currentPage === totalPages,\n      className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-chevron-right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = Pagination;\nexport default Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Pagination", "currentPage", "totalPages", "onPageChange", "getPageNumbers", "pages", "maxVisiblePages", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "className", "children", "onClick", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "page", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Common/Pagination.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n}\n\nconst Pagination: React.FC<PaginationProps> = ({ currentPage, totalPages, onPageChange }) => {\n  const getPageNumbers = () => {\n    const pages = [];\n    const maxVisiblePages = 5;\n    \n    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n    \n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\n    }\n    \n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    \n    return pages;\n  };\n\n  if (totalPages <= 1) return null;\n\n  return (\n    <div className=\"flex items-center justify-center space-x-2\">\n      {/* Previous Button */}\n      <button\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage === 1}\n        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        <i className=\"fas fa-chevron-left\"></i>\n      </button>\n\n      {/* First Page */}\n      {getPageNumbers()[0] > 1 && (\n        <>\n          <button\n            onClick={() => onPageChange(1)}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\n          >\n            1\n          </button>\n          {getPageNumbers()[0] > 2 && (\n            <span className=\"px-2 py-2 text-sm text-gray-500\">...</span>\n          )}\n        </>\n      )}\n\n      {/* Page Numbers */}\n      {getPageNumbers().map((page) => (\n        <button\n          key={page}\n          onClick={() => onPageChange(page)}\n          className={`px-3 py-2 text-sm font-medium rounded-lg ${\n            page === currentPage\n              ? 'text-white bg-indigo-600 border border-indigo-600'\n              : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'\n          }`}\n        >\n          {page}\n        </button>\n      ))}\n\n      {/* Last Page */}\n      {getPageNumbers()[getPageNumbers().length - 1] < totalPages && (\n        <>\n          {getPageNumbers()[getPageNumbers().length - 1] < totalPages - 1 && (\n            <span className=\"px-2 py-2 text-sm text-gray-500\">...</span>\n          )}\n          <button\n            onClick={() => onPageChange(totalPages)}\n            className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\n          >\n            {totalPages}\n          </button>\n        </>\n      )}\n\n      {/* Next Button */}\n      <button\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage === totalPages}\n        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n      >\n        <i className=\"fas fa-chevron-right\"></i>\n      </button>\n    </div>\n  );\n};\n\nexport default Pagination;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQ1B,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAC3F,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,eAAe,GAAG,CAAC;IAEzB,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGO,IAAI,CAACE,KAAK,CAACJ,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAACV,UAAU,EAAEK,SAAS,GAAGD,eAAe,GAAG,CAAC,CAAC;IAEnE,IAAIK,OAAO,GAAGJ,SAAS,GAAG,CAAC,GAAGD,eAAe,EAAE;MAC7CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,eAAe,GAAG,CAAC,CAAC;IACxD;IAEA,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOR,KAAK;EACd,CAAC;EAED,IAAIH,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;EAEhC,oBACEL,OAAA;IAAKkB,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzDnB,OAAA;MACEoB,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACF,WAAW,GAAG,CAAC,CAAE;MAC7CiB,QAAQ,EAAEjB,WAAW,KAAK,CAAE;MAC5Bc,SAAS,EAAC,yJAAyJ;MAAAC,QAAA,eAEnKnB,OAAA;QAAGkB,SAAS,EAAC;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,EAGRlB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,iBACtBP,OAAA,CAAAE,SAAA;MAAAiB,QAAA,gBACEnB,OAAA;QACEoB,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAAC,CAAC,CAAE;QAC/BY,SAAS,EAAC,yGAAyG;QAAAC,QAAA,EACpH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRlB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,iBACtBP,OAAA;QAAMkB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC5D;IAAA,eACD,CACH,EAGAlB,cAAc,CAAC,CAAC,CAACmB,GAAG,CAAEC,IAAI,iBACzB3B,OAAA;MAEEoB,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACqB,IAAI,CAAE;MAClCT,SAAS,EAAE,4CACTS,IAAI,KAAKvB,WAAW,GAChB,mDAAmD,GACnD,gEAAgE,EACnE;MAAAe,QAAA,EAEFQ;IAAI,GARAA,IAAI;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASH,CACT,CAAC,EAGDlB,cAAc,CAAC,CAAC,CAACA,cAAc,CAAC,CAAC,CAACqB,MAAM,GAAG,CAAC,CAAC,GAAGvB,UAAU,iBACzDL,OAAA,CAAAE,SAAA;MAAAiB,QAAA,GACGZ,cAAc,CAAC,CAAC,CAACA,cAAc,CAAC,CAAC,CAACqB,MAAM,GAAG,CAAC,CAAC,GAAGvB,UAAU,GAAG,CAAC,iBAC7DL,OAAA;QAAMkB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC5D,eACDzB,OAAA;QACEoB,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACD,UAAU,CAAE;QACxCa,SAAS,EAAC,yGAAyG;QAAAC,QAAA,EAElHd;MAAU;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACT,CACH,eAGDzB,OAAA;MACEoB,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACF,WAAW,GAAG,CAAC,CAAE;MAC7CiB,QAAQ,EAAEjB,WAAW,KAAKC,UAAW;MACrCa,SAAS,EAAC,yJAAyJ;MAAAC,QAAA,eAEnKnB,OAAA;QAAGkB,SAAS,EAAC;MAAsB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACI,EAAA,GAvFI1B,UAAqC;AAyF3C,eAAeA,UAAU;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}