{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/UploadVideoModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { createVideo, uploadVideo } from '../../store/videosSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadVideoModal = ({\n  isOpen,\n  onClose,\n  categories\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [step, setStep] = useState(1); // 1: Video info, 2: File upload\n  const [videoData, setVideoData] = useState({\n    title: '',\n    description: '',\n    category_id: ''\n  });\n  const [file, setFile] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [createdVideoId, setCreatedVideoId] = useState(null);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setVideoData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleFileChange = e => {\n    var _e$target$files;\n    const selectedFile = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (selectedFile) {\n      // Validate file type\n      const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];\n      if (!validTypes.includes(selectedFile.type)) {\n        alert('Please select a valid video file (MP4, AVI, MOV, WMV, WebM)');\n        return;\n      }\n\n      // Validate file size (500MB limit)\n      const maxSize = 500 * 1024 * 1024; // 500MB\n      if (selectedFile.size > maxSize) {\n        alert('File size must be less than 500MB');\n        return;\n      }\n      setFile(selectedFile);\n    }\n  };\n  const handleCreateVideo = async e => {\n    e.preventDefault();\n    if (!videoData.title || !videoData.category_id) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    try {\n      const result = await dispatch(createVideo({\n        title: videoData.title,\n        description: videoData.description,\n        category_id: parseInt(videoData.category_id)\n      })).unwrap();\n      setCreatedVideoId(result.id);\n      setStep(2);\n    } catch (error) {\n      alert('Failed to create video: ' + error);\n    }\n  };\n  const handleUploadFile = async () => {\n    if (!file || !createdVideoId) return;\n    setUploading(true);\n    setUploadProgress(0);\n    try {\n      // Simulate upload progress (in real implementation, you'd track actual progress)\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return prev;\n          }\n          return prev + 10;\n        });\n      }, 200);\n      await dispatch(uploadVideo({\n        id: createdVideoId,\n        file\n      })).unwrap();\n      clearInterval(progressInterval);\n      setUploadProgress(100);\n      setTimeout(() => {\n        onClose();\n        resetForm();\n      }, 1000);\n    } catch (error) {\n      alert('Failed to upload video: ' + error);\n      setUploading(false);\n      setUploadProgress(0);\n    }\n  };\n  const resetForm = () => {\n    setStep(1);\n    setVideoData({\n      title: '',\n      description: '',\n      category_id: ''\n    });\n    setFile(null);\n    setUploading(false);\n    setUploadProgress(0);\n    setCreatedVideoId(null);\n  };\n  const handleClose = () => {\n    if (!uploading) {\n      onClose();\n      resetForm();\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800\",\n          children: step === 1 ? 'Upload Video - Step 1' : 'Upload Video - Step 2'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          disabled: uploading,\n          className: \"text-gray-400 hover:text-gray-600 disabled:opacity-50\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-times text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: step === 1 ? /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleCreateVideo,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"title\",\n              value: videoData.title,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Category *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"category_id\",\n              value: videoData.category_id,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"description\",\n              value: videoData.description,\n              onChange: handleInputChange,\n              rows: 3,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleClose,\n              className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Select Video File *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              accept: \"video/*\",\n              onChange: handleFileChange,\n              disabled: uploading,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"Supported formats: MP4, AVI, MOV, WMV, WebM (Max: 500MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), file && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Selected:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this), \" \", file.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [\"Size: \", (file.size / (1024 * 1024)).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this), uploading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm text-gray-600 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Uploading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [uploadProgress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${uploadProgress}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setStep(1),\n              disabled: uploading,\n              className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50\",\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleUploadFile,\n              disabled: !file || uploading,\n              className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50\",\n              children: uploading ? 'Uploading...' : 'Upload'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadVideoModal, \"90WGlbnwaAsBCnaqN0mbqRU2FgA=\", false, function () {\n  return [useDispatch];\n});\n_c = UploadVideoModal;\nexport default UploadVideoModal;\nvar _c;\n$RefreshReg$(_c, \"UploadVideoModal\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "createVideo", "uploadVideo", "jsxDEV", "_jsxDEV", "UploadVideoModal", "isOpen", "onClose", "categories", "_s", "dispatch", "step", "setStep", "videoData", "setVideoData", "title", "description", "category_id", "file", "setFile", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "createdVideoId", "setCreatedVideoId", "handleInputChange", "e", "name", "value", "target", "prev", "handleFileChange", "_e$target$files", "selectedFile", "files", "validTypes", "includes", "type", "alert", "maxSize", "size", "handleCreateVideo", "preventDefault", "result", "parseInt", "unwrap", "id", "error", "handleUploadFile", "progressInterval", "setInterval", "clearInterval", "setTimeout", "resetForm", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "onSubmit", "onChange", "required", "map", "category", "rows", "accept", "toFixed", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/UploadVideoModal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Category } from '../../types';\nimport { createVideo, uploadVideo } from '../../store/videosSlice';\n\ninterface UploadVideoModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  categories: Category[];\n}\n\nconst UploadVideoModal: React.FC<UploadVideoModalProps> = ({ isOpen, onClose, categories }) => {\n  const dispatch = useDispatch();\n  const [step, setStep] = useState(1); // 1: Video info, 2: File upload\n  const [videoData, setVideoData] = useState({\n    title: '',\n    description: '',\n    category_id: '',\n  });\n  const [file, setFile] = useState<File | null>(null);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [createdVideoId, setCreatedVideoId] = useState<number | null>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setVideoData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0];\n    if (selectedFile) {\n      // Validate file type\n      const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];\n      if (!validTypes.includes(selectedFile.type)) {\n        alert('Please select a valid video file (MP4, AVI, MOV, WMV, WebM)');\n        return;\n      }\n      \n      // Validate file size (500MB limit)\n      const maxSize = 500 * 1024 * 1024; // 500MB\n      if (selectedFile.size > maxSize) {\n        alert('File size must be less than 500MB');\n        return;\n      }\n      \n      setFile(selectedFile);\n    }\n  };\n\n  const handleCreateVideo = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!videoData.title || !videoData.category_id) {\n      alert('Please fill in all required fields');\n      return;\n    }\n\n    try {\n      const result = await dispatch(createVideo({\n        title: videoData.title,\n        description: videoData.description,\n        category_id: parseInt(videoData.category_id),\n      })).unwrap();\n      \n      setCreatedVideoId(result.id);\n      setStep(2);\n    } catch (error) {\n      alert('Failed to create video: ' + error);\n    }\n  };\n\n  const handleUploadFile = async () => {\n    if (!file || !createdVideoId) return;\n\n    setUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Simulate upload progress (in real implementation, you'd track actual progress)\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return prev;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      await dispatch(uploadVideo({ id: createdVideoId, file })).unwrap();\n      \n      clearInterval(progressInterval);\n      setUploadProgress(100);\n      \n      setTimeout(() => {\n        onClose();\n        resetForm();\n      }, 1000);\n    } catch (error) {\n      alert('Failed to upload video: ' + error);\n      setUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const resetForm = () => {\n    setStep(1);\n    setVideoData({ title: '', description: '', category_id: '' });\n    setFile(null);\n    setUploading(false);\n    setUploadProgress(0);\n    setCreatedVideoId(null);\n  };\n\n  const handleClose = () => {\n    if (!uploading) {\n      onClose();\n      resetForm();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-800\">\n            {step === 1 ? 'Upload Video - Step 1' : 'Upload Video - Step 2'}\n          </h2>\n          <button\n            onClick={handleClose}\n            disabled={uploading}\n            className=\"text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {step === 1 ? (\n            <form onSubmit={handleCreateVideo}>\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Title *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"title\"\n                  value={videoData.title}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  required\n                />\n              </div>\n\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category *\n                </label>\n                <select\n                  name=\"category_id\"\n                  value={videoData.category_id}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  required\n                >\n                  <option value=\"\">Select a category</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description\n                </label>\n                <textarea\n                  name=\"description\"\n                  value={videoData.description}\n                  onChange={handleInputChange}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  type=\"button\"\n                  onClick={handleClose}\n                  className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\"\n                >\n                  Next\n                </button>\n              </div>\n            </form>\n          ) : (\n            <div>\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Select Video File *\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"video/*\"\n                  onChange={handleFileChange}\n                  disabled={uploading}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Supported formats: MP4, AVI, MOV, WMV, WebM (Max: 500MB)\n                </p>\n              </div>\n\n              {file && (\n                <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\n                  <p className=\"text-sm text-gray-700\">\n                    <strong>Selected:</strong> {file.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    Size: {(file.size / (1024 * 1024)).toFixed(2)} MB\n                  </p>\n                </div>\n              )}\n\n              {uploading && (\n                <div className=\"mb-4\">\n                  <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n                    <span>Uploading...</span>\n                    <span>{uploadProgress}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-indigo-600 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${uploadProgress}%` }}\n                    ></div>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  type=\"button\"\n                  onClick={() => setStep(1)}\n                  disabled={uploading}\n                  className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50\"\n                >\n                  Back\n                </button>\n                <button\n                  onClick={handleUploadFile}\n                  disabled={!file || uploading}\n                  className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50\"\n                >\n                  {uploading ? 'Uploading...' : 'Upload'}\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UploadVideoModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQnE,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC;IACzCgB,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EAEzE,MAAM2B,iBAAiB,GAAIC,CAAgF,IAAK;IAC9G,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChB,YAAY,CAACiB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,gBAAgB,GAAIL,CAAsC,IAAK;IAAA,IAAAM,eAAA;IACnE,MAAMC,YAAY,IAAAD,eAAA,GAAGN,CAAC,CAACG,MAAM,CAACK,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IACxC,IAAIC,YAAY,EAAE;MAChB;MACA,MAAME,UAAU,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;MACrF,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACH,YAAY,CAACI,IAAI,CAAC,EAAE;QAC3CC,KAAK,CAAC,6DAA6D,CAAC;QACpE;MACF;;MAEA;MACA,MAAMC,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;MACnC,IAAIN,YAAY,CAACO,IAAI,GAAGD,OAAO,EAAE;QAC/BD,KAAK,CAAC,mCAAmC,CAAC;QAC1C;MACF;MAEApB,OAAO,CAACe,YAAY,CAAC;IACvB;EACF,CAAC;EAED,MAAMQ,iBAAiB,GAAG,MAAOf,CAAkB,IAAK;IACtDA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC9B,SAAS,CAACE,KAAK,IAAI,CAACF,SAAS,CAACI,WAAW,EAAE;MAC9CsB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,IAAI;MACF,MAAMK,MAAM,GAAG,MAAMlC,QAAQ,CAACT,WAAW,CAAC;QACxCc,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBC,WAAW,EAAEH,SAAS,CAACG,WAAW;QAClCC,WAAW,EAAE4B,QAAQ,CAAChC,SAAS,CAACI,WAAW;MAC7C,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC,CAAC;MAEZrB,iBAAiB,CAACmB,MAAM,CAACG,EAAE,CAAC;MAC5BnC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdT,KAAK,CAAC,0BAA0B,GAAGS,KAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC/B,IAAI,IAAI,CAACM,cAAc,EAAE;IAE9BH,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAM2B,gBAAgB,GAAGC,WAAW,CAAC,MAAM;QACzC5B,iBAAiB,CAACQ,IAAI,IAAI;UACxB,IAAIA,IAAI,IAAI,EAAE,EAAE;YACdqB,aAAa,CAACF,gBAAgB,CAAC;YAC/B,OAAOnB,IAAI;UACb;UACA,OAAOA,IAAI,GAAG,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MAEP,MAAMrB,QAAQ,CAACR,WAAW,CAAC;QAAE6C,EAAE,EAAEvB,cAAc;QAAEN;MAAK,CAAC,CAAC,CAAC,CAAC4B,MAAM,CAAC,CAAC;MAElEM,aAAa,CAACF,gBAAgB,CAAC;MAC/B3B,iBAAiB,CAAC,GAAG,CAAC;MAEtB8B,UAAU,CAAC,MAAM;QACf9C,OAAO,CAAC,CAAC;QACT+C,SAAS,CAAC,CAAC;MACb,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAON,KAAK,EAAE;MACdT,KAAK,CAAC,0BAA0B,GAAGS,KAAK,CAAC;MACzC3B,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAM+B,SAAS,GAAGA,CAAA,KAAM;IACtB1C,OAAO,CAAC,CAAC,CAAC;IACVE,YAAY,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC,CAAC;IAC7DE,OAAO,CAAC,IAAI,CAAC;IACbE,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,CAAC,CAAC;IACpBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACnC,SAAS,EAAE;MACdb,OAAO,CAAC,CAAC;MACT+C,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,IAAI,CAAChD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKoD,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFrD,OAAA;MAAKoD,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjErD,OAAA;QAAKoD,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DrD,OAAA;UAAIoD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChD9C,IAAI,KAAK,CAAC,GAAG,uBAAuB,GAAG;QAAuB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACLzD,OAAA;UACE0D,OAAO,EAAEP,WAAY;UACrBQ,QAAQ,EAAE3C,SAAU;UACpBoC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjErD,OAAA;YAAGoD,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,KAAK;QAAAC,QAAA,EACjB9C,IAAI,KAAK,CAAC,gBACTP,OAAA;UAAM4D,QAAQ,EAAEtB,iBAAkB;UAAAe,QAAA,gBAChCrD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAOoD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEkC,IAAI,EAAC,MAAM;cACXV,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEhB,SAAS,CAACE,KAAM;cACvBkD,QAAQ,EAAEvC,iBAAkB;cAC5B8B,SAAS,EAAC,0GAA0G;cACpHU,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAOoD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEwB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEhB,SAAS,CAACI,WAAY;cAC7BgD,QAAQ,EAAEvC,iBAAkB;cAC5B8B,SAAS,EAAC,0GAA0G;cACpHU,QAAQ;cAAAT,QAAA,gBAERrD,OAAA;gBAAQyB,KAAK,EAAC,EAAE;gBAAA4B,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1CrD,UAAU,CAAC2D,GAAG,CAAEC,QAAQ,iBACvBhE,OAAA;gBAA0ByB,KAAK,EAAEuC,QAAQ,CAACrB,EAAG;gBAAAU,QAAA,EAC1CW,QAAQ,CAACxC;cAAI,GADHwC,QAAQ,CAACrB,EAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAOoD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEwB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEhB,SAAS,CAACG,WAAY;cAC7BiD,QAAQ,EAAEvC,iBAAkB;cAC5B2C,IAAI,EAAE,CAAE;cACRb,SAAS,EAAC;YAA0G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzD,OAAA;YAAKoD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCrD,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbwB,OAAO,EAAEP,WAAY;cACrBC,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzD,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbkB,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC9E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEPzD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAOoD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEkC,IAAI,EAAC,MAAM;cACXgC,MAAM,EAAC,SAAS;cAChBL,QAAQ,EAAEjC,gBAAiB;cAC3B+B,QAAQ,EAAE3C,SAAU;cACpBoC,SAAS,EAAC;YAA8H;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzI,CAAC,eACFzD,OAAA;cAAGoD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAEL3C,IAAI,iBACHd,OAAA;YAAKoD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CrD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClCrD,OAAA;gBAAAqD,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3C,IAAI,CAACU,IAAI;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACJzD,OAAA;cAAGoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,QAC7B,EAAC,CAACvC,IAAI,CAACuB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,KAChD;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN,EAEAzC,SAAS,iBACRhB,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrD,OAAA;cAAKoD,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC9DrD,OAAA;gBAAAqD,QAAA,EAAM;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBzD,OAAA;gBAAAqD,QAAA,GAAOnC,cAAc,EAAC,GAAC;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClDrD,OAAA;gBACEoD,SAAS,EAAC,4DAA4D;gBACtEgB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGnD,cAAc;gBAAI;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDzD,OAAA;YAAKoD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCrD,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbwB,OAAO,EAAEA,CAAA,KAAMlD,OAAO,CAAC,CAAC,CAAE;cAC1BmD,QAAQ,EAAE3C,SAAU;cACpBoC,SAAS,EAAC,gGAAgG;cAAAC,QAAA,EAC3G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzD,OAAA;cACE0D,OAAO,EAAEb,gBAAiB;cAC1Bc,QAAQ,EAAE,CAAC7C,IAAI,IAAIE,SAAU;cAC7BoC,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAEhGrC,SAAS,GAAG,cAAc,GAAG;YAAQ;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAtQIJ,gBAAiD;EAAA,QACpCL,WAAW;AAAA;AAAA0E,EAAA,GADxBrE,gBAAiD;AAwQvD,eAAeA,gBAAgB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}