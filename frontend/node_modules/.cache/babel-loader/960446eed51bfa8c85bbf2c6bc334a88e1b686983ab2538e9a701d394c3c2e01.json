{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { useSelector } from 'react-redux';\nimport Layout from './components/Layout/Layout';\nimport Login from './components/Auth/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContent = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useSelector(state => state.auth);\n  return isAuthenticated ? /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 28\n  }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 41\n  }, this);\n};\n_s(AppContent, \"TPD1adLOrTdsrr/HMWf3CuTzU0I=\", false, function () {\n  return [useSelector];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "Provider", "store", "useSelector", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "isAuthenticated", "state", "auth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "children", "className", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store';\nimport Layout from './components/Layout/Layout';\nimport Login from './components/Auth/Login';\n\nconst AppContent: React.FC = () => {\n  const { isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  return isAuthenticated ? <Layout /> : <Login />;\n};\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <div className=\"App\">\n        <AppContent />\n      </div>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAgB,CAAC,GAAGP,WAAW,CAAEQ,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAEzE,OAAOF,eAAe,gBAAGH,OAAA,CAACH,MAAM;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGT,OAAA,CAACF,KAAK;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACjD,CAAC;AAACP,EAAA,CAJID,UAAoB;EAAA,QACIL,WAAW;AAAA;AAAAc,EAAA,GADnCT,UAAoB;AAM1B,SAASU,GAAGA,CAAA,EAAG;EACb,oBACEX,OAAA,CAACN,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAiB,QAAA,eACrBZ,OAAA;MAAKa,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBZ,OAAA,CAACC,UAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf;AAACK,GAAA,GARQH,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}