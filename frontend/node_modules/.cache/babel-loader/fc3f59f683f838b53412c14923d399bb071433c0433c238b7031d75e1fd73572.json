{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { initializeAuth } from '../../store/authSlice';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport Dashboard from '../Dashboard/Dashboard';\nimport VideoManagement from '../Videos/VideoManagement';\nimport UserManagement from '../Users/<USER>';\nimport CategoryManagement from '../Categories/CategoryManagement';\nimport Settings from '../Settings/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    currentSection\n  } = useSelector(state => state.ui);\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n  const renderCurrentSection = () => {\n    switch (currentSection) {\n      case 'dashboard':\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 16\n        }, this);\n      case 'videos':\n        return /*#__PURE__*/_jsxDEV(VideoManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 16\n        }, this);\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 16\n        }, this);\n      case 'categories':\n        return /*#__PURE__*/_jsxDEV(CategoryManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 16\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col flex-1 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-8\",\n        children: renderCurrentSection()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"rARbT8srZvZAjy1Cny/6oKTH270=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "initializeAuth", "Sidebar", "Header", "Dashboard", "VideoManagement", "UserManagement", "CategoryManagement", "Settings", "jsxDEV", "_jsxDEV", "Layout", "_s", "dispatch", "currentSection", "state", "ui", "renderCurrentSection", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store';\nimport { initializeAuth } from '../../store/authSlice';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport Dashboard from '../Dashboard/Dashboard';\nimport VideoManagement from '../Videos/VideoManagement';\nimport UserManagement from '../Users/<USER>';\nimport CategoryManagement from '../Categories/CategoryManagement';\nimport Settings from '../Settings/Settings';\n\nconst Layout: React.FC = () => {\n  const dispatch = useDispatch();\n  const { currentSection } = useSelector((state: RootState) => state.ui);\n\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n\n  const renderCurrentSection = () => {\n    switch (currentSection) {\n      case 'dashboard':\n        return <Dashboard />;\n      case 'videos':\n        return <VideoManagement />;\n      case 'users':\n        return <UserManagement />;\n      case 'categories':\n        return <CategoryManagement />;\n      case 'settings':\n        return <Settings />;\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-200\">\n      <Sidebar />\n      \n      {/* Main content */}\n      <div className=\"flex flex-col flex-1 overflow-y-auto\">\n        <Header />\n        \n        <div className=\"p-4 md:p-8\">\n          {renderCurrentSection()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAe,CAAC,GAAGd,WAAW,CAAEe,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EAEtElB,SAAS,CAAC,MAAM;IACde,QAAQ,CAACZ,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACY,QAAQ,CAAC,CAAC;EAEd,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQH,cAAc;MACpB,KAAK,WAAW;QACd,oBAAOJ,OAAA,CAACN,SAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,QAAQ;QACX,oBAAOX,OAAA,CAACL,eAAe;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACJ,cAAc;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,YAAY;QACf,oBAAOX,OAAA,CAACH,kBAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,UAAU;QACb,oBAAOX,OAAA,CAACF,QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrB;QACE,oBAAOX,OAAA,CAACN,SAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCb,OAAA,CAACR,OAAO;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXX,OAAA;MAAKY,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDb,OAAA,CAACP,MAAM;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEVX,OAAA;QAAKY,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBN,oBAAoB,CAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CAvCID,MAAgB;EAAA,QACHZ,WAAW,EACDC,WAAW;AAAA;AAAAwB,EAAA,GAFlCb,MAAgB;AAyCtB,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}