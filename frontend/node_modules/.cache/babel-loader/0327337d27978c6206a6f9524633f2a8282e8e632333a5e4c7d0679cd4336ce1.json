{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { setSidebarOpen } from '../../store/uiSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const dispatch = useDispatch();\n  const [searchQuery, setSearchQuery] = useState('');\n  const handleSearch = e => {\n    e.preventDefault();\n    // TODO: Implement search functionality\n    console.log('Search query:', searchQuery);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between h-16 bg-white border-b border-gray-200 px-4 md:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"text-gray-500 md:hidden focus:outline-none\",\n      onClick: () => dispatch(setSidebarOpen(true)),\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-bars text-xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSearch,\n      className: \"relative w-full max-w-xs ml-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"absolute inset-y-0 left-0 flex items-center pl-3\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-search text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: searchQuery,\n        onChange: e => setSearchQuery(e.target.value),\n        className: \"w-full py-2 pl-10 pr-4 text-gray-700 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n        placeholder: \"Search...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"TA/bgwtvLN94xle/jMbdfIK7NtI=\", false, function () {\n  return [useDispatch];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "setSidebarOpen", "jsxDEV", "_jsxDEV", "Header", "_s", "dispatch", "searchQuery", "setSearch<PERSON>uery", "handleSearch", "e", "preventDefault", "console", "log", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { setSidebarOpen } from '../../store/uiSlice';\n\nconst Header: React.FC = () => {\n  const dispatch = useDispatch();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement search functionality\n    console.log('Search query:', searchQuery);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between h-16 bg-white border-b border-gray-200 px-4 md:px-8\">\n      {/* Mobile Menu Button */}\n      <button\n        className=\"text-gray-500 md:hidden focus:outline-none\"\n        onClick={() => dispatch(setSidebarOpen(true))}\n      >\n        <i className=\"fas fa-bars text-xl\"></i>\n      </button>\n\n      {/* Search Bar */}\n      <form onSubmit={handleSearch} className=\"relative w-full max-w-xs ml-auto\">\n        <span className=\"absolute inset-y-0 left-0 flex items-center pl-3\">\n          <i className=\"fas fa-search text-gray-400\"></i>\n        </span>\n        <input\n          type=\"text\"\n          value={searchQuery}\n          onChange={(e) => setSearchQuery(e.target.value)}\n          className=\"w-full py-2 pl-10 pr-4 text-gray-700 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          placeholder=\"Search...\"\n        />\n      </form>\n    </div>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,cAAc,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMU,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEN,WAAW,CAAC;EAC3C,CAAC;EAED,oBACEJ,OAAA;IAAKW,SAAS,EAAC,uFAAuF;IAAAC,QAAA,gBAEpGZ,OAAA;MACEW,SAAS,EAAC,4CAA4C;MACtDE,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAACL,cAAc,CAAC,IAAI,CAAC,CAAE;MAAAc,QAAA,eAE9CZ,OAAA;QAAGW,SAAS,EAAC;MAAqB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAGTjB,OAAA;MAAMkB,QAAQ,EAAEZ,YAAa;MAACK,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBACxEZ,OAAA;QAAMW,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAChEZ,OAAA;UAAGW,SAAS,EAAC;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACPjB,OAAA;QACEmB,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEhB,WAAY;QACnBiB,QAAQ,EAAGd,CAAC,IAAKF,cAAc,CAACE,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;QAChDT,SAAS,EAAC,6IAA6I;QACvJY,WAAW,EAAC;MAAW;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACf,EAAA,CAnCID,MAAgB;EAAA,QACHJ,WAAW;AAAA;AAAA2B,EAAA,GADxBvB,MAAgB;AAqCtB,eAAeA,MAAM;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}