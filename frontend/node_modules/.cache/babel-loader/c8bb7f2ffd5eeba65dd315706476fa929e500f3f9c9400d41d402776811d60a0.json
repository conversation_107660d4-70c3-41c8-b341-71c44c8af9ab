{"ast": null, "code": "import apiClient from './client';\nexport const videosApi = {\n  // Get all videos with pagination and filtering\n  getVideos: async (params = {}) => {\n    const response = await apiClient.get('/videos', {\n      params\n    });\n    return response.data;\n  },\n  // Get video by ID\n  getVideo: async id => {\n    const response = await apiClient.get(`/videos/${id}`);\n    return response.data.data;\n  },\n  // Create new video\n  createVideo: async data => {\n    const response = await apiClient.post('/videos', data);\n    return response.data.data;\n  },\n  // Upload video file\n  uploadVideo: async (id, file) => {\n    const formData = new FormData();\n    formData.append('video', file);\n    await apiClient.post(`/videos/${id}/upload`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // Update video\n  updateVideo: async (id, data) => {\n    const response = await apiClient.put(`/videos/${id}`, data);\n    return response.data.data;\n  },\n  // Delete video\n  deleteVideo: async id => {\n    await apiClient.delete(`/videos/${id}`);\n  },\n  // Increment view count\n  incrementViews: async id => {\n    await apiClient.post(`/videos/${id}/view`);\n  },\n  // Get video statistics\n  getStats: async () => {\n    const response = await apiClient.get('/videos/stats');\n    return response.data.data;\n  }\n};", "map": {"version": 3, "names": ["apiClient", "videosApi", "getVideos", "params", "response", "get", "data", "getVideo", "id", "createVideo", "post", "uploadVideo", "file", "formData", "FormData", "append", "headers", "updateVideo", "put", "deleteVideo", "delete", "incrementViews", "getStats"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/videos.ts"], "sourcesContent": ["import apiClient from './client';\nimport { \n  VideoWithDetails, \n  CreateVideoRequest, \n  UpdateVideoRequest, \n  VideoStats,\n  ApiResponse,\n  PaginatedResponse \n} from '../types';\n\nexport const videosApi = {\n  // Get all videos with pagination and filtering\n  getVideos: async (params: {\n    page?: number;\n    limit?: number;\n    category_id?: number;\n    status?: string;\n    user_id?: number;\n  } = {}): Promise<PaginatedResponse<VideoWithDetails>> => {\n    const response = await apiClient.get<PaginatedResponse<VideoWithDetails>>('/videos', { params });\n    return response.data;\n  },\n\n  // Get video by ID\n  getVideo: async (id: number): Promise<VideoWithDetails> => {\n    const response = await apiClient.get<ApiResponse<VideoWithDetails>>(`/videos/${id}`);\n    return response.data.data;\n  },\n\n  // Create new video\n  createVideo: async (data: CreateVideoRequest): Promise<VideoWithDetails> => {\n    const response = await apiClient.post<ApiResponse<VideoWithDetails>>('/videos', data);\n    return response.data.data;\n  },\n\n  // Upload video file\n  uploadVideo: async (id: number, file: File): Promise<void> => {\n    const formData = new FormData();\n    formData.append('video', file);\n    \n    await apiClient.post(`/videos/${id}/upload`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  },\n\n  // Update video\n  updateVideo: async (id: number, data: UpdateVideoRequest): Promise<VideoWithDetails> => {\n    const response = await apiClient.put<ApiResponse<VideoWithDetails>>(`/videos/${id}`, data);\n    return response.data.data;\n  },\n\n  // Delete video\n  deleteVideo: async (id: number): Promise<void> => {\n    await apiClient.delete(`/videos/${id}`);\n  },\n\n  // Increment view count\n  incrementViews: async (id: number): Promise<void> => {\n    await apiClient.post(`/videos/${id}/view`);\n  },\n\n  // Get video statistics\n  getStats: async (): Promise<VideoStats> => {\n    const response = await apiClient.get<ApiResponse<VideoStats>>('/videos/stats');\n    return response.data.data;\n  },\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,UAAU;AAUhC,OAAO,MAAMC,SAAS,GAAG;EACvB;EACAC,SAAS,EAAE,MAAAA,CAAOC,MAMjB,GAAG,CAAC,CAAC,KAAmD;IACvD,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAsC,SAAS,EAAE;MAAEF;IAAO,CAAC,CAAC;IAChG,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOC,EAAU,IAAgC;IACzD,MAAMJ,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAgC,WAAWG,EAAE,EAAE,CAAC;IACpF,OAAOJ,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAG,WAAW,EAAE,MAAOH,IAAwB,IAAgC;IAC1E,MAAMF,QAAQ,GAAG,MAAMJ,SAAS,CAACU,IAAI,CAAgC,SAAS,EAAEJ,IAAI,CAAC;IACrF,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAK,WAAW,EAAE,MAAAA,CAAOH,EAAU,EAAEI,IAAU,KAAoB;IAC5D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,MAAMZ,SAAS,CAACU,IAAI,CAAC,WAAWF,EAAE,SAAS,EAAEK,QAAQ,EAAE;MACrDG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,WAAW,EAAE,MAAAA,CAAOT,EAAU,EAAEF,IAAwB,KAAgC;IACtF,MAAMF,QAAQ,GAAG,MAAMJ,SAAS,CAACkB,GAAG,CAAgC,WAAWV,EAAE,EAAE,EAAEF,IAAI,CAAC;IAC1F,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAa,WAAW,EAAE,MAAOX,EAAU,IAAoB;IAChD,MAAMR,SAAS,CAACoB,MAAM,CAAC,WAAWZ,EAAE,EAAE,CAAC;EACzC,CAAC;EAED;EACAa,cAAc,EAAE,MAAOb,EAAU,IAAoB;IACnD,MAAMR,SAAS,CAACU,IAAI,CAAC,WAAWF,EAAE,OAAO,CAAC;EAC5C,CAAC;EAED;EACAc,QAAQ,EAAE,MAAAA,CAAA,KAAiC;IACzC,MAAMlB,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAA0B,eAAe,CAAC;IAC9E,OAAOD,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}