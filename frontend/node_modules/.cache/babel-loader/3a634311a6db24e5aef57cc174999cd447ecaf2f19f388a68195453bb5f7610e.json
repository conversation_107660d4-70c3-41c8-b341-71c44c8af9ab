{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authApi } from '../api/auth';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: !!localStorage.getItem('token'),\n  loading: false,\n  error: null\n};\n\n// Async thunks\nexport const login = createAsyncThunk('auth/login', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authApi.login(credentials);\n    localStorage.setItem('token', response.token);\n    localStorage.setItem('user', JSON.stringify(response.user));\n    return response;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Login failed');\n  }\n});\nexport const logout = createAsyncThunk('auth/logout', async () => {\n  authApi.logout();\n});\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    setUser: (state, action) => {\n      state.user = action.payload;\n      localStorage.setItem('user', JSON.stringify(action.payload));\n    },\n    initializeAuth: state => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          state.token = token;\n          state.user = JSON.parse(userStr);\n          state.isAuthenticated = true;\n        } catch (error) {\n          // Invalid stored data, clear it\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          state.token = null;\n          state.user = null;\n          state.isAuthenticated = false;\n        }\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Login\n    .addCase(login.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(login.fulfilled, (state, action) => {\n      state.loading = false;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.isAuthenticated = true;\n      state.error = null;\n    }).addCase(login.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n    })\n    // Logout\n    .addCase(logout.fulfilled, state => {\n      state.user = null;\n      state.token = null;\n      state.isAuthenticated = false;\n      state.error = null;\n    });\n  }\n});\nexport const {\n  clearError,\n  setUser,\n  initializeAuth\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authApi", "initialState", "user", "token", "localStorage", "getItem", "isAuthenticated", "loading", "error", "login", "credentials", "rejectWithValue", "response", "setItem", "JSON", "stringify", "_error$response", "_error$response$data", "data", "logout", "authSlice", "name", "reducers", "clearError", "state", "setUser", "action", "payload", "initializeAuth", "userStr", "parse", "removeItem", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { AuthState, LoginRequest, User } from '../types';\nimport { authApi } from '../api/auth';\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: !!localStorage.getItem('token'),\n  loading: false,\n  error: null,\n};\n\n// Async thunks\nexport const login = createAsyncThunk(\n  'auth/login',\n  async (credentials: LoginRequest, { rejectWithValue }) => {\n    try {\n      const response = await authApi.login(credentials);\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Login failed');\n    }\n  }\n);\n\nexport const logout = createAsyncThunk(\n  'auth/logout',\n  async () => {\n    authApi.logout();\n  }\n);\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setUser: (state, action: PayloadAction<User>) => {\n      state.user = action.payload;\n      localStorage.setItem('user', JSON.stringify(action.payload));\n    },\n    initializeAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      \n      if (token && userStr) {\n        try {\n          state.token = token;\n          state.user = JSON.parse(userStr);\n          state.isAuthenticated = true;\n        } catch (error) {\n          // Invalid stored data, clear it\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          state.token = null;\n          state.user = null;\n          state.isAuthenticated = false;\n        }\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Login\n      .addCase(login.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(login.fulfilled, (state, action) => {\n        state.loading = false;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(login.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n        state.user = null;\n        state.token = null;\n      })\n      // Logout\n      .addCase(logout.fulfilled, (state) => {\n        state.user = null;\n        state.token = null;\n        state.isAuthenticated = false;\n        state.error = null;\n      });\n  },\n});\n\nexport const { clearError, setUser, initializeAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAE/E,SAASC,OAAO,QAAQ,aAAa;;AAErC;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,eAAe,EAAE,CAAC,CAACF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAChDE,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,KAAK,GAAGV,gBAAgB,CACnC,YAAY,EACZ,OAAOW,WAAyB,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACxD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAACS,KAAK,CAACC,WAAW,CAAC;IACjDN,YAAY,CAACS,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACT,KAAK,CAAC;IAC7CC,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAACV,IAAI,CAAC,CAAC;IAC3D,OAAOU,QAAQ;EACjB,CAAC,CAAC,OAAOJ,KAAU,EAAE;IAAA,IAAAQ,eAAA,EAAAC,oBAAA;IACnB,OAAON,eAAe,CAAC,EAAAK,eAAA,GAAAR,KAAK,CAACI,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBT,KAAK,KAAI,cAAc,CAAC;EACvE;AACF,CACF,CAAC;AAED,OAAO,MAAMW,MAAM,GAAGpB,gBAAgB,CACpC,aAAa,EACb,YAAY;EACVC,OAAO,CAACmB,MAAM,CAAC,CAAC;AAClB,CACF,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGtB,WAAW,CAAC;EAC5BuB,IAAI,EAAE,MAAM;EACZpB,YAAY;EACZqB,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAAChB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDiB,OAAO,EAAEA,CAACD,KAAK,EAAEE,MAA2B,KAAK;MAC/CF,KAAK,CAACtB,IAAI,GAAGwB,MAAM,CAACC,OAAO;MAC3BvB,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACW,MAAM,CAACC,OAAO,CAAC,CAAC;IAC9D,CAAC;IACDC,cAAc,EAAGJ,KAAK,IAAK;MACzB,MAAMrB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMwB,OAAO,GAAGzB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIF,KAAK,IAAI0B,OAAO,EAAE;QACpB,IAAI;UACFL,KAAK,CAACrB,KAAK,GAAGA,KAAK;UACnBqB,KAAK,CAACtB,IAAI,GAAGY,IAAI,CAACgB,KAAK,CAACD,OAAO,CAAC;UAChCL,KAAK,CAAClB,eAAe,GAAG,IAAI;QAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;UACd;UACAJ,YAAY,CAAC2B,UAAU,CAAC,OAAO,CAAC;UAChC3B,YAAY,CAAC2B,UAAU,CAAC,MAAM,CAAC;UAC/BP,KAAK,CAACrB,KAAK,GAAG,IAAI;UAClBqB,KAAK,CAACtB,IAAI,GAAG,IAAI;UACjBsB,KAAK,CAAClB,eAAe,GAAG,KAAK;QAC/B;MACF;IACF;EACF,CAAC;EACD0B,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACzB,KAAK,CAAC0B,OAAO,EAAGX,KAAK,IAAK;MACjCA,KAAK,CAACjB,OAAO,GAAG,IAAI;MACpBiB,KAAK,CAAChB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD0B,OAAO,CAACzB,KAAK,CAAC2B,SAAS,EAAE,CAACZ,KAAK,EAAEE,MAAM,KAAK;MAC3CF,KAAK,CAACjB,OAAO,GAAG,KAAK;MACrBiB,KAAK,CAACtB,IAAI,GAAGwB,MAAM,CAACC,OAAO,CAACzB,IAAI;MAChCsB,KAAK,CAACrB,KAAK,GAAGuB,MAAM,CAACC,OAAO,CAACxB,KAAK;MAClCqB,KAAK,CAAClB,eAAe,GAAG,IAAI;MAC5BkB,KAAK,CAAChB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD0B,OAAO,CAACzB,KAAK,CAAC4B,QAAQ,EAAE,CAACb,KAAK,EAAEE,MAAM,KAAK;MAC1CF,KAAK,CAACjB,OAAO,GAAG,KAAK;MACrBiB,KAAK,CAAChB,KAAK,GAAGkB,MAAM,CAACC,OAAiB;MACtCH,KAAK,CAAClB,eAAe,GAAG,KAAK;MAC7BkB,KAAK,CAACtB,IAAI,GAAG,IAAI;MACjBsB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC;IACD;IAAA,CACC+B,OAAO,CAACf,MAAM,CAACiB,SAAS,EAAGZ,KAAK,IAAK;MACpCA,KAAK,CAACtB,IAAI,GAAG,IAAI;MACjBsB,KAAK,CAACrB,KAAK,GAAG,IAAI;MAClBqB,KAAK,CAAClB,eAAe,GAAG,KAAK;MAC7BkB,KAAK,CAAChB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEe,UAAU;EAAEE,OAAO;EAAEG;AAAe,CAAC,GAAGR,SAAS,CAACkB,OAAO;AACxE,eAAelB,SAAS,CAACmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}