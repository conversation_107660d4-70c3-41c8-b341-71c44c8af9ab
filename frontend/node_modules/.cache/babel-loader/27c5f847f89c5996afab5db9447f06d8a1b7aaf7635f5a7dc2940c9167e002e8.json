{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchVideos } from '../../store/videosSlice';\nimport { fetchActiveCategories } from '../../store/categoriesSlice';\nimport VideoGrid from './VideoGrid';\nimport VideoFilters from './VideoFilters';\nimport UploadVideoModal from './UploadVideoModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoManagement = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    videos,\n    loading,\n    pagination\n  } = useSelector(state => state.videos);\n  const {\n    activeCategories\n  } = useSelector(state => state.categories);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [filters, setFilters] = useState({\n    category_id: 0,\n    status: '',\n    page: 1,\n    limit: 12\n  });\n  useEffect(() => {\n    dispatch(fetchActiveCategories());\n  }, [dispatch]);\n  useEffect(() => {\n    dispatch(fetchVideos(filters));\n  }, [dispatch, filters]);\n  const handleFilterChange = newFilters => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters,\n      page: 1\n    }));\n  };\n  const handlePageChange = page => {\n    setFilters(prev => ({\n      ...prev,\n      page\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap items-center justify-between gap-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800\",\n        children: \"Video Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowUploadModal(true),\n        className: \"flex items-center px-4 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-upload mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), \"Upload Video\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VideoFilters, {\n      categories: activeCategories,\n      filters: filters,\n      onFilterChange: handleFilterChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VideoGrid, {\n      videos: videos,\n      loading: loading,\n      pagination: pagination,\n      onPageChange: handlePageChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), showUploadModal && /*#__PURE__*/_jsxDEV(UploadVideoModal, {\n      isOpen: showUploadModal,\n      onClose: () => setShowUploadModal(false),\n      categories: activeCategories\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoManagement, \"MxlzdvZE2uymTJqGX/DrsfquB6c=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = VideoManagement;\nexport default VideoManagement;\nvar _c;\n$RefreshReg$(_c, \"VideoManagement\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchVideos", "fetchActiveCategories", "VideoGrid", "VideoFilters", "UploadVideoModal", "jsxDEV", "_jsxDEV", "VideoManagement", "_s", "dispatch", "videos", "loading", "pagination", "state", "activeCategories", "categories", "showUploadModal", "setShowUploadModal", "filters", "setFilters", "category_id", "status", "page", "limit", "handleFilterChange", "newFilters", "prev", "handlePageChange", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onFilterChange", "onPageChange", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store';\nimport { fetchVideos } from '../../store/videosSlice';\nimport { fetchActiveCategories } from '../../store/categoriesSlice';\nimport VideoGrid from './VideoGrid';\nimport VideoFilters from './VideoFilters';\nimport UploadVideoModal from './UploadVideoModal';\n\nconst VideoManagement: React.FC = () => {\n  const dispatch = useDispatch();\n  const { videos, loading, pagination } = useSelector((state: RootState) => state.videos);\n  const { activeCategories } = useSelector((state: RootState) => state.categories);\n  \n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [filters, setFilters] = useState({\n    category_id: 0,\n    status: '',\n    page: 1,\n    limit: 12,\n  });\n\n  useEffect(() => {\n    dispatch(fetchActiveCategories());\n  }, [dispatch]);\n\n  useEffect(() => {\n    dispatch(fetchVideos(filters));\n  }, [dispatch, filters]);\n\n  const handleFilterChange = (newFilters: Partial<typeof filters>) => {\n    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));\n  };\n\n  const handlePageChange = (page: number) => {\n    setFilters(prev => ({ ...prev, page }));\n  };\n\n  return (\n    <div>\n      <div className=\"flex flex-wrap items-center justify-between gap-4 mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800\">Video Management</h1>\n        <button\n          onClick={() => setShowUploadModal(true)}\n          className=\"flex items-center px-4 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all\"\n        >\n          <i className=\"fas fa-upload mr-2\"></i>\n          Upload Video\n        </button>\n      </div>\n\n      {/* Filters */}\n      <VideoFilters\n        categories={activeCategories}\n        filters={filters}\n        onFilterChange={handleFilterChange}\n      />\n\n      {/* Video Grid */}\n      <VideoGrid\n        videos={videos}\n        loading={loading}\n        pagination={pagination}\n        onPageChange={handlePageChange}\n      />\n\n      {/* Upload Modal */}\n      {showUploadModal && (\n        <UploadVideoModal\n          isOpen={showUploadModal}\n          onClose={() => setShowUploadModal(false)}\n          categories={activeCategories}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default VideoManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,MAAM;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAEc,KAAgB,IAAKA,KAAK,CAACH,MAAM,CAAC;EACvF,MAAM;IAAEI;EAAiB,CAAC,GAAGf,WAAW,CAAEc,KAAgB,IAAKA,KAAK,CAACE,UAAU,CAAC;EAEhF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC;IACrCuB,WAAW,EAAE,CAAC;IACdC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACda,QAAQ,CAACR,qBAAqB,CAAC,CAAC,CAAC;EACnC,CAAC,EAAE,CAACQ,QAAQ,CAAC,CAAC;EAEdb,SAAS,CAAC,MAAM;IACda,QAAQ,CAACT,WAAW,CAACkB,OAAO,CAAC,CAAC;EAChC,CAAC,EAAE,CAACT,QAAQ,EAAES,OAAO,CAAC,CAAC;EAEvB,MAAMM,kBAAkB,GAAIC,UAAmC,IAAK;IAClEN,UAAU,CAACO,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD,UAAU;MAAEH,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMK,gBAAgB,GAAIL,IAAY,IAAK;IACzCH,UAAU,CAACO,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEJ;IAAK,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,oBACEhB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAKuB,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrEtB,OAAA;QAAIuB,SAAS,EAAC,kCAAkC;QAAAD,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE3B,OAAA;QACE4B,OAAO,EAAEA,CAAA,KAAMjB,kBAAkB,CAAC,IAAI,CAAE;QACxCY,SAAS,EAAC,oMAAoM;QAAAD,QAAA,gBAE9MtB,OAAA;UAAGuB,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAExC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3B,OAAA,CAACH,YAAY;MACXY,UAAU,EAAED,gBAAiB;MAC7BI,OAAO,EAAEA,OAAQ;MACjBiB,cAAc,EAAEX;IAAmB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGF3B,OAAA,CAACJ,SAAS;MACRQ,MAAM,EAAEA,MAAO;MACfC,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEA,UAAW;MACvBwB,YAAY,EAAET;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAGDjB,eAAe,iBACdV,OAAA,CAACF,gBAAgB;MACfiC,MAAM,EAAErB,eAAgB;MACxBsB,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAAC,KAAK,CAAE;MACzCF,UAAU,EAAED;IAAiB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzB,EAAA,CAnEID,eAAyB;EAAA,QACZT,WAAW,EACYC,WAAW,EACtBA,WAAW;AAAA;AAAAwC,EAAA,GAHpChC,eAAyB;AAqE/B,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}