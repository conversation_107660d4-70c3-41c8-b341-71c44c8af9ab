{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoFilters.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoFilters = ({\n  categories,\n  filters,\n  onFilterChange\n}) => {\n  const statusOptions = [{\n    value: '',\n    label: 'All Status'\n  }, {\n    value: 'published',\n    label: 'Published'\n  }, {\n    value: 'processing',\n    label: 'Processing'\n  }, {\n    value: 'private',\n    label: 'Private'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.category_id,\n          onChange: e => onFilterChange({\n            category_id: parseInt(e.target.value)\n          }),\n          className: \"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 0,\n            children: \"All Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category.id,\n            children: category.name\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-1\",\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.status,\n          onChange: e => onFilterChange({\n            status: e.target.value\n          }),\n          className: \"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\",\n          children: statusOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: option.value,\n            children: option.label\n          }, option.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), (filters.category_id !== 0 || filters.status !== '') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-end\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onFilterChange({\n            category_id: 0,\n            status: ''\n          }),\n          className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 underline\",\n          children: \"Clear Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = VideoFilters;\nexport default VideoFilters;\nvar _c;\n$RefreshReg$(_c, \"VideoFilters\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "VideoFilters", "categories", "filters", "onFilterChange", "statusOptions", "value", "label", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category_id", "onChange", "e", "parseInt", "target", "map", "category", "id", "name", "status", "option", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoFilters.tsx"], "sourcesContent": ["import React from 'react';\nimport { Category } from '../../types';\n\ninterface VideoFiltersProps {\n  categories: Category[];\n  filters: {\n    category_id: number;\n    status: string;\n  };\n  onFilterChange: (filters: Partial<{ category_id: number; status: string }>) => void;\n}\n\nconst VideoFilters: React.FC<VideoFiltersProps> = ({ categories, filters, onFilterChange }) => {\n  const statusOptions = [\n    { value: '', label: 'All Status' },\n    { value: 'published', label: 'Published' },\n    { value: 'processing', label: 'Processing' },\n    { value: 'private', label: 'Private' },\n  ];\n\n  return (\n    <div className=\"mb-6\">\n      <div className=\"flex flex-wrap gap-4\">\n        {/* Category Filter */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Category\n          </label>\n          <select\n            value={filters.category_id}\n            onChange={(e) => onFilterChange({ category_id: parseInt(e.target.value) })}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n          >\n            <option value={0}>All Categories</option>\n            {categories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Status Filter */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Status\n          </label>\n          <select\n            value={filters.status}\n            onChange={(e) => onFilterChange({ status: e.target.value })}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n          >\n            {statusOptions.map((option) => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Clear Filters */}\n        {(filters.category_id !== 0 || filters.status !== '') && (\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => onFilterChange({ category_id: 0, status: '' })}\n              className=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 underline\"\n            >\n              Clear Filters\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoFilters;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1B,MAAMC,YAAyC,GAAGA,CAAC;EAAEC,UAAU;EAAEC,OAAO;EAAEC;AAAe,CAAC,KAAK;EAC7F,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAa,CAAC,EAClC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,MAAM;IAAAC,QAAA,eACnBT,OAAA;MAAKQ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCT,OAAA;QAAAS,QAAA,gBACET,OAAA;UAAOQ,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRb,OAAA;UACEM,KAAK,EAAEH,OAAO,CAACW,WAAY;UAC3BC,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC;YAAEU,WAAW,EAAEG,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACZ,KAAK;UAAE,CAAC,CAAE;UAC3EE,SAAS,EAAC,4HAA4H;UAAAC,QAAA,gBAEtIT,OAAA;YAAQM,KAAK,EAAE,CAAE;YAAAG,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCX,UAAU,CAACiB,GAAG,CAAEC,QAAQ,iBACvBpB,OAAA;YAA0BM,KAAK,EAAEc,QAAQ,CAACC,EAAG;YAAAZ,QAAA,EAC1CW,QAAQ,CAACE;UAAI,GADHF,QAAQ,CAACC,EAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNb,OAAA;QAAAS,QAAA,gBACET,OAAA;UAAOQ,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRb,OAAA;UACEM,KAAK,EAAEH,OAAO,CAACoB,MAAO;UACtBR,QAAQ,EAAGC,CAAC,IAAKZ,cAAc,CAAC;YAAEmB,MAAM,EAAEP,CAAC,CAACE,MAAM,CAACZ;UAAM,CAAC,CAAE;UAC5DE,SAAS,EAAC,4HAA4H;UAAAC,QAAA,EAErIJ,aAAa,CAACc,GAAG,CAAEK,MAAM,iBACxBxB,OAAA;YAA2BM,KAAK,EAAEkB,MAAM,CAAClB,KAAM;YAAAG,QAAA,EAC5Ce,MAAM,CAACjB;UAAK,GADFiB,MAAM,CAAClB,KAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL,CAACV,OAAO,CAACW,WAAW,KAAK,CAAC,IAAIX,OAAO,CAACoB,MAAM,KAAK,EAAE,kBAClDvB,OAAA;QAAKQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BT,OAAA;UACEyB,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC;YAAEU,WAAW,EAAE,CAAC;YAAES,MAAM,EAAE;UAAG,CAAC,CAAE;UAC9Df,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAC1E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GA9DIzB,YAAyC;AAgE/C,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}