{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchVideoStats } from '../../store/videosSlice';\nimport { fetchVideos } from '../../store/videosSlice';\nimport StatsCard from './StatsCard';\nimport RecentVideos from './RecentVideos';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    stats\n  } = useSelector(state => state.videos);\n  const {\n    loading\n  } = useSelector(state => state.videos);\n  useEffect(() => {\n    dispatch(fetchVideoStats());\n    dispatch(fetchVideos({\n      limit: 5\n    })); // Fetch recent videos\n  }, [dispatch]);\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const formatNumber = num => {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n  if (loading && !stats) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-800 mb-6\",\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-4\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Videos\",\n        value: stats ? formatNumber(stats.total_videos) : '0',\n        icon: \"fas fa-video\",\n        color: \"indigo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Users\",\n        value: \"84\" // This would come from user stats\n        ,\n        icon: \"fas fa-users\",\n        color: \"green\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Storage Used\",\n        value: stats ? formatFileSize(stats.total_storage) : '0 GB',\n        icon: \"fas fa-hdd\",\n        color: \"yellow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Views\",\n        value: stats ? formatNumber(stats.total_views) : '0',\n        icon: \"fas fa-chart-bar\",\n        color: \"red\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RecentVideos, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"Lyqmmas3fqprGZykg2EbW/650i8=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "fetchVideoStats", "fetchVideos", "StatsCard", "RecentVideos", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "dispatch", "stats", "state", "videos", "loading", "limit", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatNumber", "num", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "value", "total_videos", "icon", "color", "total_storage", "total_views", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store';\nimport { fetchVideoStats } from '../../store/videosSlice';\nimport { fetchVideos } from '../../store/videosSlice';\nimport StatsCard from './StatsCard';\nimport RecentVideos from './RecentVideos';\n\nconst Dashboard: React.FC = () => {\n  const dispatch = useDispatch();\n  const { stats } = useSelector((state: RootState) => state.videos);\n  const { loading } = useSelector((state: RootState) => state.videos);\n\n  useEffect(() => {\n    dispatch(fetchVideoStats());\n    dispatch(fetchVideos({ limit: 5 })); // Fetch recent videos\n  }, [dispatch]);\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatNumber = (num: number): string => {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n\n  if (loading && !stats) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold text-gray-800 mb-6\">Dashboard</h1>\n      \n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-4\">\n        <StatsCard\n          title=\"Total Videos\"\n          value={stats ? formatNumber(stats.total_videos) : '0'}\n          icon=\"fas fa-video\"\n          color=\"indigo\"\n        />\n        <StatsCard\n          title=\"Total Users\"\n          value=\"84\" // This would come from user stats\n          icon=\"fas fa-users\"\n          color=\"green\"\n        />\n        <StatsCard\n          title=\"Storage Used\"\n          value={stats ? formatFileSize(stats.total_storage) : '0 GB'}\n          icon=\"fas fa-hdd\"\n          color=\"yellow\"\n        />\n        <StatsCard\n          title=\"Total Views\"\n          value={stats ? formatNumber(stats.total_views) : '0'}\n          icon=\"fas fa-chart-bar\"\n          color=\"red\"\n        />\n      </div>\n\n      {/* Recent Videos */}\n      <RecentVideos />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAM,CAAC,GAAGV,WAAW,CAAEW,KAAgB,IAAKA,KAAK,CAACC,MAAM,CAAC;EACjE,MAAM;IAAEC;EAAQ,CAAC,GAAGb,WAAW,CAAEW,KAAgB,IAAKA,KAAK,CAACC,MAAM,CAAC;EAEnEd,SAAS,CAAC,MAAM;IACdW,QAAQ,CAACR,eAAe,CAAC,CAAC,CAAC;IAC3BQ,QAAQ,CAACP,WAAW,CAAC;MAAEY,KAAK,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,MAAMM,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/C,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,YAAY,GAAIC,GAAW,IAAa;IAC5C,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACzC,CAAC,MAAM,IAAIE,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACtC;IACA,OAAOE,GAAG,CAACC,QAAQ,CAAC,CAAC;EACvB,CAAC;EAED,IAAIf,OAAO,IAAI,CAACH,KAAK,EAAE;IACrB,oBACEJ,OAAA;MAAKuB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDxB,OAAA;QAAKuB,SAAS,EAAC;MAAkE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAAwB,QAAA,gBACExB,OAAA;MAAIuB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGpE5B,OAAA;MAAKuB,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxExB,OAAA,CAACH,SAAS;QACRgC,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAE1B,KAAK,GAAGgB,YAAY,CAAChB,KAAK,CAAC2B,YAAY,CAAC,GAAG,GAAI;QACtDC,IAAI,EAAC,cAAc;QACnBC,KAAK,EAAC;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACF5B,OAAA,CAACH,SAAS;QACRgC,KAAK,EAAC,aAAa;QACnBC,KAAK,EAAC,IAAI,CAAC;QAAA;QACXE,IAAI,EAAC,cAAc;QACnBC,KAAK,EAAC;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACF5B,OAAA,CAACH,SAAS;QACRgC,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAE1B,KAAK,GAAGK,cAAc,CAACL,KAAK,CAAC8B,aAAa,CAAC,GAAG,MAAO;QAC5DF,IAAI,EAAC,YAAY;QACjBC,KAAK,EAAC;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACF5B,OAAA,CAACH,SAAS;QACRgC,KAAK,EAAC,aAAa;QACnBC,KAAK,EAAE1B,KAAK,GAAGgB,YAAY,CAAChB,KAAK,CAAC+B,WAAW,CAAC,GAAG,GAAI;QACrDH,IAAI,EAAC,kBAAkB;QACvBC,KAAK,EAAC;MAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5B,OAAA,CAACF,YAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAvEID,SAAmB;EAAA,QACNR,WAAW,EACVC,WAAW,EACTA,WAAW;AAAA;AAAA0C,EAAA,GAH3BnC,SAAmB;AAyEzB,eAAeA,SAAS;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}