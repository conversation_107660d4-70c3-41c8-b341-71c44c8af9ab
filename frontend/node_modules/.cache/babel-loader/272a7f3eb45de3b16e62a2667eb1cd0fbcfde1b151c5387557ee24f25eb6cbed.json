{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { categoriesApi } from '../api/categories';\nconst initialState = {\n  categories: [],\n  activeCategories: [],\n  currentCategory: null,\n  loading: false,\n  error: null,\n  pagination: null\n};\n\n// Async thunks\nexport const fetchCategories = createAsyncThunk('categories/fetchCategories', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await categoriesApi.getCategories(params);\n    return response;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to fetch categories');\n  }\n});\nexport const fetchActiveCategories = createAsyncThunk('categories/fetchActiveCategories', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const categories = await categoriesApi.getActiveCategories();\n    return categories;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Failed to fetch active categories');\n  }\n});\nexport const fetchCategory = createAsyncThunk('categories/fetchCategory', async ({\n  id,\n  includeStats = false\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const category = await categoriesApi.getCategory(id, includeStats);\n    return category;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Failed to fetch category');\n  }\n});\nexport const createCategory = createAsyncThunk('categories/createCategory', async (data, {\n  rejectWithValue\n}) => {\n  try {\n    const category = await categoriesApi.createCategory(data);\n    return category;\n  } catch (error) {\n    var _error$response4, _error$response4$data;\n    return rejectWithValue(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || 'Failed to create category');\n  }\n});\nexport const updateCategory = createAsyncThunk('categories/updateCategory', async ({\n  id,\n  data\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const category = await categoriesApi.updateCategory(id, data);\n    return category;\n  } catch (error) {\n    var _error$response5, _error$response5$data;\n    return rejectWithValue(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.error) || 'Failed to update category');\n  }\n});\nexport const deleteCategory = createAsyncThunk('categories/deleteCategory', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    await categoriesApi.deleteCategory(id);\n    return id;\n  } catch (error) {\n    var _error$response6, _error$response6$data;\n    return rejectWithValue(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.error) || 'Failed to delete category');\n  }\n});\n\n// Categories slice\nconst categoriesSlice = createSlice({\n  name: 'categories',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    clearCurrentCategory: state => {\n      state.currentCategory = null;\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch categories\n    .addCase(fetchCategories.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchCategories.fulfilled, (state, action) => {\n      state.loading = false;\n      state.categories = action.payload.data.categories || action.payload.data;\n      state.pagination = action.payload.data.pagination || null;\n    }).addCase(fetchCategories.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    })\n    // Fetch active categories\n    .addCase(fetchActiveCategories.fulfilled, (state, action) => {\n      state.activeCategories = action.payload;\n    })\n    // Fetch single category\n    .addCase(fetchCategory.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchCategory.fulfilled, (state, action) => {\n      state.loading = false;\n      state.currentCategory = action.payload;\n    }).addCase(fetchCategory.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    })\n    // Create category\n    .addCase(createCategory.fulfilled, (state, action) => {\n      const categoryWithStats = {\n        ...action.payload,\n        video_count: 0\n      };\n      state.categories.unshift(categoryWithStats);\n      state.activeCategories.unshift(action.payload);\n    })\n    // Update category\n    .addCase(updateCategory.fulfilled, (state, action) => {\n      const index = state.categories.findIndex(c => c.id === action.payload.id);\n      if (index !== -1) {\n        state.categories[index] = {\n          ...state.categories[index],\n          ...action.payload\n        };\n      }\n      const activeIndex = state.activeCategories.findIndex(c => c.id === action.payload.id);\n      if (activeIndex !== -1) {\n        state.activeCategories[activeIndex] = action.payload;\n      }\n      if (state.currentCategory && state.currentCategory.id === action.payload.id) {\n        state.currentCategory = {\n          ...state.currentCategory,\n          ...action.payload\n        };\n      }\n    })\n    // Delete category\n    .addCase(deleteCategory.fulfilled, (state, action) => {\n      state.categories = state.categories.filter(c => c.id !== action.payload);\n      state.activeCategories = state.activeCategories.filter(c => c.id !== action.payload);\n      if (state.currentCategory && state.currentCategory.id === action.payload) {\n        state.currentCategory = null;\n      }\n    });\n  }\n});\nexport const {\n  clearError,\n  clearCurrentCategory\n} = categoriesSlice.actions;\nexport default categoriesSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "categoriesApi", "initialState", "categories", "activeCategories", "currentCategory", "loading", "error", "pagination", "fetchCategories", "params", "rejectWithValue", "response", "getCategories", "_error$response", "_error$response$data", "data", "fetchActiveCategories", "_", "getActiveCategories", "_error$response2", "_error$response2$data", "fetchCategory", "id", "includeStats", "category", "getCategory", "_error$response3", "_error$response3$data", "createCategory", "_error$response4", "_error$response4$data", "updateCategory", "_error$response5", "_error$response5$data", "deleteCategory", "_error$response6", "_error$response6$data", "categoriesSlice", "name", "reducers", "clearError", "state", "clearCurrentCategory", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "categoryWithStats", "video_count", "unshift", "index", "findIndex", "c", "activeIndex", "filter", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/categoriesSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { Category, CategoryWithStats, CreateCategoryRequest, UpdateCategoryRequest } from '../types';\nimport { categoriesApi } from '../api/categories';\n\ninterface CategoriesState {\n  categories: CategoryWithStats[];\n  activeCategories: Category[];\n  currentCategory: CategoryWithStats | null;\n  loading: boolean;\n  error: string | null;\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    limit: number;\n  } | null;\n}\n\nconst initialState: CategoriesState = {\n  categories: [],\n  activeCategories: [],\n  currentCategory: null,\n  loading: false,\n  error: null,\n  pagination: null,\n};\n\n// Async thunks\nexport const fetchCategories = createAsyncThunk(\n  'categories/fetchCategories',\n  async (params: {\n    page?: number;\n    limit?: number;\n    include_stats?: boolean;\n  } = {}, { rejectWithValue }) => {\n    try {\n      const response = await categoriesApi.getCategories(params);\n      return response;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to fetch categories');\n    }\n  }\n);\n\nexport const fetchActiveCategories = createAsyncThunk(\n  'categories/fetchActiveCategories',\n  async (_, { rejectWithValue }) => {\n    try {\n      const categories = await categoriesApi.getActiveCategories();\n      return categories;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to fetch active categories');\n    }\n  }\n);\n\nexport const fetchCategory = createAsyncThunk(\n  'categories/fetchCategory',\n  async ({ id, includeStats = false }: { id: number; includeStats?: boolean }, { rejectWithValue }) => {\n    try {\n      const category = await categoriesApi.getCategory(id, includeStats);\n      return category;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to fetch category');\n    }\n  }\n);\n\nexport const createCategory = createAsyncThunk(\n  'categories/createCategory',\n  async (data: CreateCategoryRequest, { rejectWithValue }) => {\n    try {\n      const category = await categoriesApi.createCategory(data);\n      return category;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to create category');\n    }\n  }\n);\n\nexport const updateCategory = createAsyncThunk(\n  'categories/updateCategory',\n  async ({ id, data }: { id: number; data: UpdateCategoryRequest }, { rejectWithValue }) => {\n    try {\n      const category = await categoriesApi.updateCategory(id, data);\n      return category;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to update category');\n    }\n  }\n);\n\nexport const deleteCategory = createAsyncThunk(\n  'categories/deleteCategory',\n  async (id: number, { rejectWithValue }) => {\n    try {\n      await categoriesApi.deleteCategory(id);\n      return id;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.error || 'Failed to delete category');\n    }\n  }\n);\n\n// Categories slice\nconst categoriesSlice = createSlice({\n  name: 'categories',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    clearCurrentCategory: (state) => {\n      state.currentCategory = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch categories\n      .addCase(fetchCategories.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchCategories.fulfilled, (state, action) => {\n        state.loading = false;\n        state.categories = action.payload.data.categories || action.payload.data;\n        state.pagination = action.payload.data.pagination || null;\n      })\n      .addCase(fetchCategories.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch active categories\n      .addCase(fetchActiveCategories.fulfilled, (state, action) => {\n        state.activeCategories = action.payload;\n      })\n      // Fetch single category\n      .addCase(fetchCategory.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchCategory.fulfilled, (state, action) => {\n        state.loading = false;\n        state.currentCategory = action.payload;\n      })\n      .addCase(fetchCategory.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // Create category\n      .addCase(createCategory.fulfilled, (state, action) => {\n        const categoryWithStats: CategoryWithStats = {\n          ...action.payload,\n          video_count: 0,\n        };\n        state.categories.unshift(categoryWithStats);\n        state.activeCategories.unshift(action.payload);\n      })\n      // Update category\n      .addCase(updateCategory.fulfilled, (state, action) => {\n        const index = state.categories.findIndex(c => c.id === action.payload.id);\n        if (index !== -1) {\n          state.categories[index] = { ...state.categories[index], ...action.payload };\n        }\n        \n        const activeIndex = state.activeCategories.findIndex(c => c.id === action.payload.id);\n        if (activeIndex !== -1) {\n          state.activeCategories[activeIndex] = action.payload;\n        }\n        \n        if (state.currentCategory && state.currentCategory.id === action.payload.id) {\n          state.currentCategory = { ...state.currentCategory, ...action.payload };\n        }\n      })\n      // Delete category\n      .addCase(deleteCategory.fulfilled, (state, action) => {\n        state.categories = state.categories.filter(c => c.id !== action.payload);\n        state.activeCategories = state.activeCategories.filter(c => c.id !== action.payload);\n        if (state.currentCategory && state.currentCategory.id === action.payload) {\n          state.currentCategory = null;\n        }\n      });\n  },\n});\n\nexport const { clearError, clearCurrentCategory } = categoriesSlice.actions;\nexport default categoriesSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAEhE,SAASC,aAAa,QAAQ,mBAAmB;AAgBjD,MAAMC,YAA6B,GAAG;EACpCC,UAAU,EAAE,EAAE;EACdC,gBAAgB,EAAE,EAAE;EACpBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGT,gBAAgB,CAC7C,4BAA4B,EAC5B,OAAOU,MAIN,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC9B,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMX,aAAa,CAACY,aAAa,CAACH,MAAM,CAAC;IAC1D,OAAOE,QAAQ;EACjB,CAAC,CAAC,OAAOL,KAAU,EAAE;IAAA,IAAAO,eAAA,EAAAC,oBAAA;IACnB,OAAOJ,eAAe,CAAC,EAAAG,eAAA,GAAAP,KAAK,CAACK,QAAQ,cAAAE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBR,KAAK,KAAI,4BAA4B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMU,qBAAqB,GAAGjB,gBAAgB,CACnD,kCAAkC,EAClC,OAAOkB,CAAC,EAAE;EAAEP;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMR,UAAU,GAAG,MAAMF,aAAa,CAACkB,mBAAmB,CAAC,CAAC;IAC5D,OAAOhB,UAAU;EACnB,CAAC,CAAC,OAAOI,KAAU,EAAE;IAAA,IAAAa,gBAAA,EAAAC,qBAAA;IACnB,OAAOV,eAAe,CAAC,EAAAS,gBAAA,GAAAb,KAAK,CAACK,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBd,KAAK,KAAI,mCAAmC,CAAC;EAC5F;AACF,CACF,CAAC;AAED,OAAO,MAAMe,aAAa,GAAGtB,gBAAgB,CAC3C,0BAA0B,EAC1B,OAAO;EAAEuB,EAAE;EAAEC,YAAY,GAAG;AAA8C,CAAC,EAAE;EAAEb;AAAgB,CAAC,KAAK;EACnG,IAAI;IACF,MAAMc,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,WAAW,CAACH,EAAE,EAAEC,YAAY,CAAC;IAClE,OAAOC,QAAQ;EACjB,CAAC,CAAC,OAAOlB,KAAU,EAAE;IAAA,IAAAoB,gBAAA,EAAAC,qBAAA;IACnB,OAAOjB,eAAe,CAAC,EAAAgB,gBAAA,GAAApB,KAAK,CAACK,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBrB,KAAK,KAAI,0BAA0B,CAAC;EACnF;AACF,CACF,CAAC;AAED,OAAO,MAAMsB,cAAc,GAAG7B,gBAAgB,CAC5C,2BAA2B,EAC3B,OAAOgB,IAA2B,EAAE;EAAEL;AAAgB,CAAC,KAAK;EAC1D,IAAI;IACF,MAAMc,QAAQ,GAAG,MAAMxB,aAAa,CAAC4B,cAAc,CAACb,IAAI,CAAC;IACzD,OAAOS,QAAQ;EACjB,CAAC,CAAC,OAAOlB,KAAU,EAAE;IAAA,IAAAuB,gBAAA,EAAAC,qBAAA;IACnB,OAAOpB,eAAe,CAAC,EAAAmB,gBAAA,GAAAvB,KAAK,CAACK,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBxB,KAAK,KAAI,2BAA2B,CAAC;EACpF;AACF,CACF,CAAC;AAED,OAAO,MAAMyB,cAAc,GAAGhC,gBAAgB,CAC5C,2BAA2B,EAC3B,OAAO;EAAEuB,EAAE;EAAEP;AAAkD,CAAC,EAAE;EAAEL;AAAgB,CAAC,KAAK;EACxF,IAAI;IACF,MAAMc,QAAQ,GAAG,MAAMxB,aAAa,CAAC+B,cAAc,CAACT,EAAE,EAAEP,IAAI,CAAC;IAC7D,OAAOS,QAAQ;EACjB,CAAC,CAAC,OAAOlB,KAAU,EAAE;IAAA,IAAA0B,gBAAA,EAAAC,qBAAA;IACnB,OAAOvB,eAAe,CAAC,EAAAsB,gBAAA,GAAA1B,KAAK,CAACK,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsB3B,KAAK,KAAI,2BAA2B,CAAC;EACpF;AACF,CACF,CAAC;AAED,OAAO,MAAM4B,cAAc,GAAGnC,gBAAgB,CAC5C,2BAA2B,EAC3B,OAAOuB,EAAU,EAAE;EAAEZ;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF,MAAMV,aAAa,CAACkC,cAAc,CAACZ,EAAE,CAAC;IACtC,OAAOA,EAAE;EACX,CAAC,CAAC,OAAOhB,KAAU,EAAE;IAAA,IAAA6B,gBAAA,EAAAC,qBAAA;IACnB,OAAO1B,eAAe,CAAC,EAAAyB,gBAAA,GAAA7B,KAAK,CAACK,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsB9B,KAAK,KAAI,2BAA2B,CAAC;EACpF;AACF,CACF,CAAC;;AAED;AACA,MAAM+B,eAAe,GAAGvC,WAAW,CAAC;EAClCwC,IAAI,EAAE,YAAY;EAClBrC,YAAY;EACZsC,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACnC,KAAK,GAAG,IAAI;IACpB,CAAC;IACDoC,oBAAoB,EAAGD,KAAK,IAAK;MAC/BA,KAAK,CAACrC,eAAe,GAAG,IAAI;IAC9B;EACF,CAAC;EACDuC,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACrC,eAAe,CAACsC,OAAO,EAAGL,KAAK,IAAK;MAC3CA,KAAK,CAACpC,OAAO,GAAG,IAAI;MACpBoC,KAAK,CAACnC,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuC,OAAO,CAACrC,eAAe,CAACuC,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MACrDP,KAAK,CAACpC,OAAO,GAAG,KAAK;MACrBoC,KAAK,CAACvC,UAAU,GAAG8C,MAAM,CAACC,OAAO,CAAClC,IAAI,CAACb,UAAU,IAAI8C,MAAM,CAACC,OAAO,CAAClC,IAAI;MACxE0B,KAAK,CAAClC,UAAU,GAAGyC,MAAM,CAACC,OAAO,CAAClC,IAAI,CAACR,UAAU,IAAI,IAAI;IAC3D,CAAC,CAAC,CACDsC,OAAO,CAACrC,eAAe,CAAC0C,QAAQ,EAAE,CAACT,KAAK,EAAEO,MAAM,KAAK;MACpDP,KAAK,CAACpC,OAAO,GAAG,KAAK;MACrBoC,KAAK,CAACnC,KAAK,GAAG0C,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCJ,OAAO,CAAC7B,qBAAqB,CAAC+B,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MAC3DP,KAAK,CAACtC,gBAAgB,GAAG6C,MAAM,CAACC,OAAO;IACzC,CAAC;IACD;IAAA,CACCJ,OAAO,CAACxB,aAAa,CAACyB,OAAO,EAAGL,KAAK,IAAK;MACzCA,KAAK,CAACpC,OAAO,GAAG,IAAI;MACpBoC,KAAK,CAACnC,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuC,OAAO,CAACxB,aAAa,CAAC0B,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MACnDP,KAAK,CAACpC,OAAO,GAAG,KAAK;MACrBoC,KAAK,CAACrC,eAAe,GAAG4C,MAAM,CAACC,OAAO;IACxC,CAAC,CAAC,CACDJ,OAAO,CAACxB,aAAa,CAAC6B,QAAQ,EAAE,CAACT,KAAK,EAAEO,MAAM,KAAK;MAClDP,KAAK,CAACpC,OAAO,GAAG,KAAK;MACrBoC,KAAK,CAACnC,KAAK,GAAG0C,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCJ,OAAO,CAACjB,cAAc,CAACmB,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MACpD,MAAMG,iBAAoC,GAAG;QAC3C,GAAGH,MAAM,CAACC,OAAO;QACjBG,WAAW,EAAE;MACf,CAAC;MACDX,KAAK,CAACvC,UAAU,CAACmD,OAAO,CAACF,iBAAiB,CAAC;MAC3CV,KAAK,CAACtC,gBAAgB,CAACkD,OAAO,CAACL,MAAM,CAACC,OAAO,CAAC;IAChD,CAAC;IACD;IAAA,CACCJ,OAAO,CAACd,cAAc,CAACgB,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MACpD,MAAMM,KAAK,GAAGb,KAAK,CAACvC,UAAU,CAACqD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAK0B,MAAM,CAACC,OAAO,CAAC3B,EAAE,CAAC;MACzE,IAAIgC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBb,KAAK,CAACvC,UAAU,CAACoD,KAAK,CAAC,GAAG;UAAE,GAAGb,KAAK,CAACvC,UAAU,CAACoD,KAAK,CAAC;UAAE,GAAGN,MAAM,CAACC;QAAQ,CAAC;MAC7E;MAEA,MAAMQ,WAAW,GAAGhB,KAAK,CAACtC,gBAAgB,CAACoD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAK0B,MAAM,CAACC,OAAO,CAAC3B,EAAE,CAAC;MACrF,IAAImC,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBhB,KAAK,CAACtC,gBAAgB,CAACsD,WAAW,CAAC,GAAGT,MAAM,CAACC,OAAO;MACtD;MAEA,IAAIR,KAAK,CAACrC,eAAe,IAAIqC,KAAK,CAACrC,eAAe,CAACkB,EAAE,KAAK0B,MAAM,CAACC,OAAO,CAAC3B,EAAE,EAAE;QAC3EmB,KAAK,CAACrC,eAAe,GAAG;UAAE,GAAGqC,KAAK,CAACrC,eAAe;UAAE,GAAG4C,MAAM,CAACC;QAAQ,CAAC;MACzE;IACF,CAAC;IACD;IAAA,CACCJ,OAAO,CAACX,cAAc,CAACa,SAAS,EAAE,CAACN,KAAK,EAAEO,MAAM,KAAK;MACpDP,KAAK,CAACvC,UAAU,GAAGuC,KAAK,CAACvC,UAAU,CAACwD,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAK0B,MAAM,CAACC,OAAO,CAAC;MACxER,KAAK,CAACtC,gBAAgB,GAAGsC,KAAK,CAACtC,gBAAgB,CAACuD,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAK0B,MAAM,CAACC,OAAO,CAAC;MACpF,IAAIR,KAAK,CAACrC,eAAe,IAAIqC,KAAK,CAACrC,eAAe,CAACkB,EAAE,KAAK0B,MAAM,CAACC,OAAO,EAAE;QACxER,KAAK,CAACrC,eAAe,GAAG,IAAI;MAC9B;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEoC,UAAU;EAAEE;AAAqB,CAAC,GAAGL,eAAe,CAACsB,OAAO;AAC3E,eAAetB,eAAe,CAACuB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}