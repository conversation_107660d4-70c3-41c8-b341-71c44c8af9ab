{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './authSlice';\nimport videosReducer from './videosSlice';\nimport categoriesReducer from './categoriesSlice';\nimport uiReducer from './uiSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    videos: videosReducer,\n    categories: categoriesReducer,\n    ui: uiReducer\n  }\n});", "map": {"version": 3, "names": ["configureStore", "authReducer", "videosReducer", "categoriesReducer", "uiReducer", "store", "reducer", "auth", "videos", "categories", "ui"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './authSlice';\nimport videosReducer from './videosSlice';\nimport categoriesReducer from './categoriesSlice';\nimport uiReducer from './uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    videos: videosReducer,\n    categories: categoriesReducer,\n    ui: uiReducer,\n  },\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,iBAAiB,MAAM,mBAAmB;AACjD,OAAOC,SAAS,MAAM,WAAW;AAEjC,OAAO,MAAMC,KAAK,GAAGL,cAAc,CAAC;EAClCM,OAAO,EAAE;IACPC,IAAI,EAAEN,WAAW;IACjBO,MAAM,EAAEN,aAAa;IACrBO,UAAU,EAAEN,iBAAiB;IAC7BO,EAAE,EAAEN;EACN;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}