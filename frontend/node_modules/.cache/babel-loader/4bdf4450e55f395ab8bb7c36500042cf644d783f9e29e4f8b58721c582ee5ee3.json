{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  sidebarOpen: false,\n  currentSection: 'dashboard',\n  loading: false,\n  error: null\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: state => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action) => {\n      state.sidebarOpen = action.payload;\n    },\n    setCurrentSection: (state, action) => {\n      state.currentSection = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    }\n  }\n});\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setCurrentSection,\n  setLoading,\n  setError,\n  clearError\n} = uiSlice.actions;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarOpen", "currentSection", "loading", "error", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarOpen", "action", "payload", "setCurrentSection", "setLoading", "setError", "clearError", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { UIState } from '../types';\n\nconst initialState: UIState = {\n  sidebarOpen: false,\n  currentSection: 'dashboard',\n  loading: false,\n  error: null,\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    setCurrentSection: (state, action: PayloadAction<string>) => {\n      state.currentSection = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setCurrentSection,\n  setLoading,\n  setError,\n  clearError,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,MAAMC,YAAqB,GAAG;EAC5BC,WAAW,EAAE,KAAK;EAClBC,cAAc,EAAE,WAAW;EAC3BC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,OAAO,GAAGN,WAAW,CAAC;EAC1BO,IAAI,EAAE,IAAI;EACVN,YAAY;EACZO,QAAQ,EAAE;IACRC,aAAa,EAAGC,KAAK,IAAK;MACxBA,KAAK,CAACR,WAAW,GAAG,CAACQ,KAAK,CAACR,WAAW;IACxC,CAAC;IACDS,cAAc,EAAEA,CAACD,KAAK,EAAEE,MAA8B,KAAK;MACzDF,KAAK,CAACR,WAAW,GAAGU,MAAM,CAACC,OAAO;IACpC,CAAC;IACDC,iBAAiB,EAAEA,CAACJ,KAAK,EAAEE,MAA6B,KAAK;MAC3DF,KAAK,CAACP,cAAc,GAAGS,MAAM,CAACC,OAAO;IACvC,CAAC;IACDE,UAAU,EAAEA,CAACL,KAAK,EAAEE,MAA8B,KAAK;MACrDF,KAAK,CAACN,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAChC,CAAC;IACDG,QAAQ,EAAEA,CAACN,KAAK,EAAEE,MAAoC,KAAK;MACzDF,KAAK,CAACL,KAAK,GAAGO,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDI,UAAU,EAAGP,KAAK,IAAK;MACrBA,KAAK,CAACL,KAAK,GAAG,IAAI;IACpB;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXI,aAAa;EACbE,cAAc;EACdG,iBAAiB;EACjBC,UAAU;EACVC,QAAQ;EACRC;AACF,CAAC,GAAGX,OAAO,CAACY,OAAO;AAEnB,eAAeZ,OAAO,CAACa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}