{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoGrid.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport VideoCard from './VideoCard';\nimport VideoPlayerModal from './VideoPlayerModal';\nimport Pagination from '../Common/Pagination';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoGrid = ({\n  videos,\n  loading,\n  pagination,\n  onPageChange\n}) => {\n  _s();\n  const [selectedVideo, setSelectedVideo] = useState(null);\n  const handleVideoPlay = video => {\n    setSelectedVideo(video);\n  };\n  const handleClosePlayer = () => {\n    setSelectedVideo(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n      children: Array.from({\n        length: 8\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-40 bg-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-3 bg-gray-300 rounded w-2/3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this);\n  }\n  if (videos.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-video text-6xl text-gray-300 mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No videos found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Upload your first video to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n      children: videos.map(video => /*#__PURE__*/_jsxDEV(VideoCard, {\n        video: video,\n        onPlay: handleVideoPlay\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), pagination && pagination.totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: pagination.currentPage,\n        totalPages: pagination.totalPages,\n        onPageChange: onPageChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }, this), selectedVideo && /*#__PURE__*/_jsxDEV(VideoPlayerModal, {\n      video: selectedVideo,\n      isOpen: !!selectedVideo,\n      onClose: handleClosePlayer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(VideoGrid, \"sxgy0ooEX+Me+y0n/sMclmbujtY=\");\n_c = VideoGrid;\nexport default VideoGrid;\nvar _c;\n$RefreshReg$(_c, \"VideoGrid\");", "map": {"version": 3, "names": ["React", "useState", "VideoCard", "VideoPlayerModal", "Pagination", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoGrid", "videos", "loading", "pagination", "onPageChange", "_s", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "handleVideoPlay", "video", "handleClosePlayer", "className", "children", "Array", "from", "length", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onPlay", "id", "totalPages", "currentPage", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoGrid.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { VideoWithDetails } from '../../types';\nimport VideoCard from './VideoCard';\nimport VideoPlayerModal from './VideoPlayerModal';\nimport Pagination from '../Common/Pagination';\n\ninterface VideoGridProps {\n  videos: VideoWithDetails[];\n  loading: boolean;\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    limit: number;\n  } | null;\n  onPageChange: (page: number) => void;\n}\n\nconst VideoGrid: React.FC<VideoGridProps> = ({ videos, loading, pagination, onPageChange }) => {\n  const [selectedVideo, setSelectedVideo] = useState<VideoWithDetails | null>(null);\n\n  const handleVideoPlay = (video: VideoWithDetails) => {\n    setSelectedVideo(video);\n  };\n\n  const handleClosePlayer = () => {\n    setSelectedVideo(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n        {Array.from({ length: 8 }).map((_, index) => (\n          <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n            <div className=\"w-full h-40 bg-gray-300\"></div>\n            <div className=\"p-4\">\n              <div className=\"h-4 bg-gray-300 rounded mb-2\"></div>\n              <div className=\"h-3 bg-gray-300 rounded w-2/3\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (videos.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <i className=\"fas fa-video text-6xl text-gray-300 mb-4\"></i>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No videos found</h3>\n        <p className=\"text-gray-500\">Upload your first video to get started.</p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n        {videos.map((video) => (\n          <VideoCard\n            key={video.id}\n            video={video}\n            onPlay={handleVideoPlay}\n          />\n        ))}\n      </div>\n\n      {/* Pagination */}\n      {pagination && pagination.totalPages > 1 && (\n        <div className=\"mt-8\">\n          <Pagination\n            currentPage={pagination.currentPage}\n            totalPages={pagination.totalPages}\n            onPageChange={onPageChange}\n          />\n        </div>\n      )}\n\n      {/* Video Player Modal */}\n      {selectedVideo && (\n        <VideoPlayerModal\n          video={selectedVideo}\n          isOpen={!!selectedVideo}\n          onClose={handleClosePlayer}\n        />\n      )}\n    </>\n  );\n};\n\nexport default VideoGrid;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,UAAU,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAc9C,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAA0B,IAAI,CAAC;EAEjF,MAAMgB,eAAe,GAAIC,KAAuB,IAAK;IACnDF,gBAAgB,CAACE,KAAK,CAAC;EACzB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BH,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKc,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACtCrB,OAAA;QAAiBc,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBACtFf,OAAA;UAAKc,SAAS,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CzB,OAAA;UAAKc,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBf,OAAA;YAAKc,SAAS,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDzB,OAAA;YAAKc,SAAS,EAAC;UAA+B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA,GALEJ,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,IAAIrB,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE;IACvB,oBACElB,OAAA;MAAKc,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCf,OAAA;QAAGc,SAAS,EAAC;MAA0C;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5DzB,OAAA;QAAIc,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAe;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3EzB,OAAA;QAAGc,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC;EAEV;EAEA,oBACEzB,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA;MAAKc,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFX,MAAM,CAACe,GAAG,CAAEP,KAAK,iBAChBZ,OAAA,CAACJ,SAAS;QAERgB,KAAK,EAAEA,KAAM;QACbc,MAAM,EAAEf;MAAgB,GAFnBC,KAAK,CAACe,EAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGd,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLnB,UAAU,IAAIA,UAAU,CAACsB,UAAU,GAAG,CAAC,iBACtC5B,OAAA;MAAKc,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBf,OAAA,CAACF,UAAU;QACT+B,WAAW,EAAEvB,UAAU,CAACuB,WAAY;QACpCD,UAAU,EAAEtB,UAAU,CAACsB,UAAW;QAClCrB,YAAY,EAAEA;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAhB,aAAa,iBACZT,OAAA,CAACH,gBAAgB;MACfe,KAAK,EAAEH,aAAc;MACrBqB,MAAM,EAAE,CAAC,CAACrB,aAAc;MACxBsB,OAAO,EAAElB;IAAkB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;EAAA,eACD,CAAC;AAEP,CAAC;AAACjB,EAAA,CAtEIL,SAAmC;AAAA6B,EAAA,GAAnC7B,SAAmC;AAwEzC,eAAeA,SAAS;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}