{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setCurrentSection, setSidebarOpen } from '../../store/uiSlice';\nimport { logout } from '../../store/authSlice';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    sidebarOpen,\n    currentSection\n  } = useSelector(state => state.ui);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const menuItems = [{\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: 'fas fa-tachometer-alt'\n  }, {\n    id: 'videos',\n    label: 'Videos',\n    icon: 'fas fa-video'\n  }, {\n    id: 'users',\n    label: 'Users',\n    icon: 'fas fa-users'\n  }, {\n    id: 'categories',\n    label: 'Categories',\n    icon: 'fas fa-tags'\n  }, {\n    id: 'settings',\n    label: 'Settings',\n    icon: 'fas fa-cog'\n  }];\n  const handleSectionChange = sectionId => {\n    dispatch(setCurrentSection(sectionId));\n    // Close sidebar on mobile after selection\n    if (window.innerWidth < 768) {\n      dispatch(setSidebarOpen(false));\n    }\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-20 bg-black bg-opacity-50 md:hidden\",\n      onClick: () => dispatch(setSidebarOpen(false))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-y-0 left-0 z-30 w-64 px-4 py-7 overflow-y-auto text-white transition-transform duration-300 ease-in-out transform bg-gray-800 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:relative md:translate-x-0`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between px-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-2xl font-bold\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle text-indigo-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Vortex\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-gray-400 md:hidden hover:text-white\",\n          onClick: () => dispatch(setSidebarOpen(false)),\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-times text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-10\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleSectionChange(item.id),\n          className: `w-full flex items-center px-4 py-3 mt-4 text-left transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white ${currentSection === item.id ? 'bg-indigo-500 text-white' : 'text-gray-300'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `${item.icon} w-5 h-5`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mx-4 font-medium\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 w-full p-4 border-t border-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"w-10 h-10 rounded-full\",\n            src: (user === null || user === void 0 ? void 0 : user.avatar) || `https://ui-avatars.com/api/?name=${user === null || user === void 0 ? void 0 : user.username}&background=7e22ce&color=ffffff`,\n            alt: \"User Avatar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-white\",\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"text-xs text-indigo-400 hover:underline\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"hX5GZYGK8lEzxtRWBrqOcpxknFY=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "setCurrentSection", "setSidebarOpen", "logout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "_s", "dispatch", "sidebarOpen", "currentSection", "state", "ui", "user", "auth", "menuItems", "id", "label", "icon", "handleSectionChange", "sectionId", "window", "innerWidth", "handleLogout", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "src", "avatar", "username", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store';\nimport { setCurrentSection, setSidebarOpen } from '../../store/uiSlice';\nimport { logout } from '../../store/authSlice';\n\nconst Sidebar: React.FC = () => {\n  const dispatch = useDispatch();\n  const { sidebarOpen, currentSection } = useSelector((state: RootState) => state.ui);\n  const { user } = useSelector((state: RootState) => state.auth);\n\n  const menuItems = [\n    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },\n    { id: 'videos', label: 'Videos', icon: 'fas fa-video' },\n    { id: 'users', label: 'Users', icon: 'fas fa-users' },\n    { id: 'categories', label: 'Categories', icon: 'fas fa-tags' },\n    { id: 'settings', label: 'Settings', icon: 'fas fa-cog' },\n  ];\n\n  const handleSectionChange = (sectionId: string) => {\n    dispatch(setCurrentSection(sectionId));\n    // Close sidebar on mobile after selection\n    if (window.innerWidth < 768) {\n      dispatch(setSidebarOpen(false));\n    }\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n  };\n\n  return (\n    <>\n      {/* Overlay for mobile */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-20 bg-black bg-opacity-50 md:hidden\"\n          onClick={() => dispatch(setSidebarOpen(false))}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-30 w-64 px-4 py-7 overflow-y-auto text-white transition-transform duration-300 ease-in-out transform bg-gray-800 ${\n          sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n        } md:relative md:translate-x-0`}\n      >\n        {/* Logo */}\n        <div className=\"flex items-center justify-between px-2\">\n          <div className=\"flex items-center space-x-2 text-2xl font-bold\">\n            <i className=\"fas fa-play-circle text-indigo-400\"></i>\n            <span>Vortex</span>\n          </div>\n          {/* Close button for mobile */}\n          <button\n            className=\"text-gray-400 md:hidden hover:text-white\"\n            onClick={() => dispatch(setSidebarOpen(false))}\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Navigation Links */}\n        <nav className=\"mt-10\">\n          {menuItems.map((item) => (\n            <button\n              key={item.id}\n              onClick={() => handleSectionChange(item.id)}\n              className={`w-full flex items-center px-4 py-3 mt-4 text-left transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white ${\n                currentSection === item.id\n                  ? 'bg-indigo-500 text-white'\n                  : 'text-gray-300'\n              }`}\n            >\n              <i className={`${item.icon} w-5 h-5`}></i>\n              <span className=\"mx-4 font-medium\">{item.label}</span>\n            </button>\n          ))}\n        </nav>\n\n        {/* User Profile */}\n        <div className=\"absolute bottom-0 left-0 w-full p-4 border-t border-gray-700\">\n          <div className=\"flex items-center\">\n            <img\n              className=\"w-10 h-10 rounded-full\"\n              src={user?.avatar || `https://ui-avatars.com/api/?name=${user?.username}&background=7e22ce&color=ffffff`}\n              alt=\"User Avatar\"\n            />\n            <div className=\"ml-3 flex-1\">\n              <p className=\"text-sm font-medium text-white\">{user?.username}</p>\n              <button\n                onClick={handleLogout}\n                className=\"text-xs text-indigo-400 hover:underline\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,qBAAqB;AACvE,SAASC,MAAM,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,WAAW;IAAEC;EAAe,CAAC,GAAGZ,WAAW,CAAEa,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EACnF,MAAM;IAAEC;EAAK,CAAC,GAAGf,WAAW,CAAEa,KAAgB,IAAKA,KAAK,CAACG,IAAI,CAAC;EAE9D,MAAMC,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAwB,CAAC,EACtE;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAe,CAAC,EACvD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAe,CAAC,EACrD;IAAEF,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAc,CAAC,EAC9D;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAa,CAAC,CAC1D;EAED,MAAMC,mBAAmB,GAAIC,SAAiB,IAAK;IACjDZ,QAAQ,CAACT,iBAAiB,CAACqB,SAAS,CAAC,CAAC;IACtC;IACA,IAAIC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MAC3Bd,QAAQ,CAACR,cAAc,CAAC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzBf,QAAQ,CAACP,MAAM,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,oBACEE,OAAA,CAAAE,SAAA;IAAAmB,QAAA,GAEGf,WAAW,iBACVN,OAAA;MACEsB,SAAS,EAAC,qDAAqD;MAC/DC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAACR,cAAc,CAAC,KAAK,CAAC;IAAE;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACF,eAGD3B,OAAA;MACEsB,SAAS,EAAE,6IACThB,WAAW,GAAG,eAAe,GAAG,mBAAmB,+BACrB;MAAAe,QAAA,gBAGhCrB,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAD,QAAA,gBACrDrB,OAAA;UAAKsB,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC7DrB,OAAA;YAAGsB,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtD3B,OAAA;YAAAqB,QAAA,EAAM;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEN3B,OAAA;UACEsB,SAAS,EAAC,0CAA0C;UACpDC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAACR,cAAc,CAAC,KAAK,CAAC,CAAE;UAAAwB,QAAA,eAE/CrB,OAAA;YAAGsB,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,OAAO;QAAAD,QAAA,EACnBT,SAAS,CAACgB,GAAG,CAAEC,IAAI,iBAClB7B,OAAA;UAEEuB,OAAO,EAAEA,CAAA,KAAMP,mBAAmB,CAACa,IAAI,CAAChB,EAAE,CAAE;UAC5CS,SAAS,EAAE,kIACTf,cAAc,KAAKsB,IAAI,CAAChB,EAAE,GACtB,0BAA0B,GAC1B,eAAe,EAClB;UAAAQ,QAAA,gBAEHrB,OAAA;YAAGsB,SAAS,EAAE,GAAGO,IAAI,CAACd,IAAI;UAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1C3B,OAAA;YAAMsB,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAEQ,IAAI,CAACf;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATjDE,IAAI,CAAChB,EAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,8DAA8D;QAAAD,QAAA,eAC3ErB,OAAA;UAAKsB,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCrB,OAAA;YACEsB,SAAS,EAAC,wBAAwB;YAClCQ,GAAG,EAAE,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,MAAM,KAAI,oCAAoCrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,iCAAkC;YACzGC,GAAG,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF3B,OAAA;YAAKsB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1BrB,OAAA;cAAGsB,SAAS,EAAC,gCAAgC;cAAAD,QAAA,EAAEX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB;YAAQ;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE3B,OAAA;cACEuB,OAAO,EAAEH,YAAa;cACtBE,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EACpD;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACvB,EAAA,CAhGID,OAAiB;EAAA,QACJT,WAAW,EACYC,WAAW,EAClCA,WAAW;AAAA;AAAAuC,EAAA,GAHxB/B,OAAiB;AAkGvB,eAAeA,OAAO;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}