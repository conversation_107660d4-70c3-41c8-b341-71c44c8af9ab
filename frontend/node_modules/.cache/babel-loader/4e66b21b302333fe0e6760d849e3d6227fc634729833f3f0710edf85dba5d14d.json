{"ast": null, "code": "import apiClient from './client';\nexport const authApi = {\n  // Login user\n  login: async credentials => {\n    const response = await apiClient.post('/auth/login', credentials);\n    return response.data.data;\n  },\n  // Get current user profile\n  getProfile: async () => {\n    const response = await apiClient.get('/users/me');\n    return response.data.data;\n  },\n  // Logout (client-side only)\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authApi", "login", "credentials", "response", "post", "data", "getProfile", "get", "logout", "localStorage", "removeItem"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/auth.ts"], "sourcesContent": ["import apiClient from './client';\nimport { LoginRequest, LoginResponse, User, ApiResponse } from '../types';\n\nexport const authApi = {\n  // Login user\n  login: async (credentials: LoginRequest): Promise<LoginResponse> => {\n    const response = await apiClient.post<ApiResponse<LoginResponse>>('/auth/login', credentials);\n    return response.data.data;\n  },\n\n  // Get current user profile\n  getProfile: async (): Promise<User> => {\n    const response = await apiClient.get<ApiResponse<User>>('/users/me');\n    return response.data.data;\n  },\n\n  // Logout (client-side only)\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,UAAU;AAGhC,OAAO,MAAMC,OAAO,GAAG;EACrB;EACAC,KAAK,EAAE,MAAOC,WAAyB,IAA6B;IAClE,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAA6B,aAAa,EAAEF,WAAW,CAAC;IAC7F,OAAOC,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAC,UAAU,EAAE,MAAAA,CAAA,KAA2B;IACrC,MAAMH,QAAQ,GAAG,MAAMJ,SAAS,CAACQ,GAAG,CAAoB,WAAW,CAAC;IACpE,OAAOJ,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAG,MAAM,EAAEA,CAAA,KAAM;IACZC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;EACjC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}