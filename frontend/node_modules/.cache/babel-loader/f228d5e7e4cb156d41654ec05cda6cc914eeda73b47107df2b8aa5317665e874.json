{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/RecentVideos.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecentVideos = () => {\n  _s();\n  const {\n    videos,\n    loading\n  } = useSelector(state => state.videos);\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getStatusBadge = status => {\n    const statusClasses = {\n      published: 'bg-green-100 text-green-800',\n      processing: 'bg-yellow-100 text-yellow-800',\n      private: 'bg-gray-100 text-gray-800'\n    };\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[status] || statusClasses.private}`,\n      children: status.charAt(0).toUpperCase() + status.slice(1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800\",\n          children: \"Recently Added Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800\",\n        children: \"Recently Added Videos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: videos.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-video text-4xl mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No videos found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white\",\n          children: videos.slice(0, 5).map(video => {\n            var _video$category;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"border-b border-gray-200 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-20 h-12 bg-gray-200 rounded-md flex items-center justify-center\",\n                    children: video.thumbnail ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"w-20 h-12 object-cover rounded-md\",\n                      src: video.thumbnail,\n                      alt: \"Video thumbnail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 64,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-play text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 70,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 62,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: video.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 74,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: ((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.name) || 'Uncategorized'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 75,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 73,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: formatDate(video.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: getStatusBadge(video.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, video.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(RecentVideos, \"FJc3dDjlLAm0CfwXR5cQLjCfEDw=\", false, function () {\n  return [useSelector];\n});\n_c = RecentVideos;\nexport default RecentVideos;\nvar _c;\n$RefreshReg$(_c, \"RecentVideos\");", "map": {"version": 3, "names": ["React", "useSelector", "jsxDEV", "_jsxDEV", "RecentVideos", "_s", "videos", "loading", "state", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "getStatusBadge", "status", "statusClasses", "published", "processing", "private", "className", "children", "char<PERSON>t", "toUpperCase", "slice", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "video", "_video$category", "thumbnail", "src", "alt", "title", "category", "name", "created_at", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/RecentVideos.tsx"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../../store';\n\nconst RecentVideos: React.FC = () => {\n  const { videos, loading } = useSelector((state: RootState) => state.videos);\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusClasses = {\n      published: 'bg-green-100 text-green-800',\n      processing: 'bg-yellow-100 text-yellow-800',\n      private: 'bg-gray-100 text-gray-800',\n    };\n\n    return (\n      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[status as keyof typeof statusClasses] || statusClasses.private}`}>\n        {status.charAt(0).toUpperCase() + status.slice(1)}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md\">\n        <div className=\"p-4 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-800\">Recently Added Videos</h2>\n        </div>\n        <div className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-4 border-b\">\n        <h2 className=\"text-xl font-semibold text-gray-800\">Recently Added Videos</h2>\n      </div>\n      <div className=\"overflow-x-auto\">\n        {videos.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            <i className=\"fas fa-video text-4xl mb-4\"></i>\n            <p>No videos found</p>\n          </div>\n        ) : (\n          <table className=\"min-w-full\">\n            <tbody className=\"bg-white\">\n              {videos.slice(0, 5).map((video) => (\n                <tr key={video.id} className=\"border-b border-gray-200 hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-20 h-12 bg-gray-200 rounded-md flex items-center justify-center\">\n                        {video.thumbnail ? (\n                          <img\n                            className=\"w-20 h-12 object-cover rounded-md\"\n                            src={video.thumbnail}\n                            alt=\"Video thumbnail\"\n                          />\n                        ) : (\n                          <i className=\"fas fa-play text-gray-400\"></i>\n                        )}\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{video.title}</div>\n                        <div className=\"text-sm text-gray-500\">\n                          {video.category?.name || 'Uncategorized'}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {formatDate(video.created_at)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {getStatusBadge(video.status)}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RecentVideos;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAGN,WAAW,CAAEO,KAAgB,IAAKA,KAAK,CAACF,MAAM,CAAC;EAE3E,MAAMG,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,aAAa,GAAG;MACpBC,SAAS,EAAE,6BAA6B;MACxCC,UAAU,EAAE,+BAA+B;MAC3CC,OAAO,EAAE;IACX,CAAC;IAED,oBACEnB,OAAA;MAAMoB,SAAS,EAAE,4DAA4DJ,aAAa,CAACD,MAAM,CAA+B,IAAIC,aAAa,CAACG,OAAO,EAAG;MAAAE,QAAA,EACzJN,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGR,MAAM,CAACS,KAAK,CAAC,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEX,CAAC;EAED,IAAIxB,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CrB,OAAA;QAAKoB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BrB,OAAA;UAAIoB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACN5B,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrB,OAAA;UAAKoB,SAAS,EAAC;QAAwE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKoB,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC5CrB,OAAA;MAAKoB,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BrB,OAAA;QAAIoB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eACN5B,OAAA;MAAKoB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BlB,MAAM,CAAC0B,MAAM,KAAK,CAAC,gBAClB7B,OAAA;QAAKoB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CrB,OAAA;UAAGoB,SAAS,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C5B,OAAA;UAAAqB,QAAA,EAAG;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,gBAEN5B,OAAA;QAAOoB,SAAS,EAAC,YAAY;QAAAC,QAAA,eAC3BrB,OAAA;UAAOoB,SAAS,EAAC,UAAU;UAAAC,QAAA,EACxBlB,MAAM,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACM,GAAG,CAAEC,KAAK;YAAA,IAAAC,eAAA;YAAA,oBAC5BhC,OAAA;cAAmBoB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACtErB,OAAA;gBAAIoB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCrB,OAAA;kBAAKoB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrB,OAAA;oBAAKoB,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,EAC/EU,KAAK,CAACE,SAAS,gBACdjC,OAAA;sBACEoB,SAAS,EAAC,mCAAmC;sBAC7Cc,GAAG,EAAEH,KAAK,CAACE,SAAU;sBACrBE,GAAG,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,gBAEF5B,OAAA;sBAAGoB,SAAS,EAAC;oBAA2B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAC7C;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN5B,OAAA;oBAAKoB,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBrB,OAAA;sBAAKoB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEU,KAAK,CAACK;oBAAK;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtE5B,OAAA;sBAAKoB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACnC,EAAAW,eAAA,GAAAD,KAAK,CAACM,QAAQ,cAAAL,eAAA,uBAAdA,eAAA,CAAgBM,IAAI,KAAI;oBAAe;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5B,OAAA;gBAAIoB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9Df,UAAU,CAACyB,KAAK,CAACQ,UAAU;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACL5B,OAAA;gBAAIoB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACxCP,cAAc,CAACiB,KAAK,CAAChB,MAAM;cAAC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA,GA3BEG,KAAK,CAACS,EAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4Bb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA1FID,YAAsB;EAAA,QACEH,WAAW;AAAA;AAAA2C,EAAA,GADnCxC,YAAsB;AA4F5B,eAAeA,YAAY;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}