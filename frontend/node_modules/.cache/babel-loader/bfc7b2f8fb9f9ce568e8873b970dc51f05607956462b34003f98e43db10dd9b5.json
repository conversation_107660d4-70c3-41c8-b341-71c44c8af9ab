{"ast": null, "code": "import apiClient from './client';\nexport const categoriesApi = {\n  // Get all categories with pagination\n  getCategories: async (params = {}) => {\n    const response = await apiClient.get('/categories', {\n      params\n    });\n    return response.data;\n  },\n  // Get active categories (for dropdowns)\n  getActiveCategories: async () => {\n    const response = await apiClient.get('/categories/active');\n    return response.data.data;\n  },\n  // Get category by ID\n  getCategory: async (id, includeStats = false) => {\n    const response = await apiClient.get(`/categories/${id}`, {\n      params: {\n        include_stats: includeStats\n      }\n    });\n    return response.data.data;\n  },\n  // Create new category (admin only)\n  createCategory: async data => {\n    const response = await apiClient.post('/admin/categories', data);\n    return response.data.data;\n  },\n  // Update category (admin only)\n  updateCategory: async (id, data) => {\n    const response = await apiClient.put(`/admin/categories/${id}`, data);\n    return response.data.data;\n  },\n  // Delete category (admin only)\n  deleteCategory: async id => {\n    await apiClient.delete(`/admin/categories/${id}`);\n  },\n  // Get category statistics\n  getStats: async () => {\n    const response = await apiClient.get('/categories/stats');\n    return response.data.data;\n  }\n};", "map": {"version": 3, "names": ["apiClient", "categoriesApi", "getCategories", "params", "response", "get", "data", "getActiveCategories", "getCategory", "id", "includeStats", "include_stats", "createCategory", "post", "updateCategory", "put", "deleteCategory", "delete", "getStats"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/categories.ts"], "sourcesContent": ["import apiClient from './client';\nimport { \n  Category, \n  CategoryWithStats, \n  CreateCategoryRequest, \n  UpdateCategoryRequest,\n  ApiResponse,\n  PaginatedResponse \n} from '../types';\n\nexport const categoriesApi = {\n  // Get all categories with pagination\n  getCategories: async (params: {\n    page?: number;\n    limit?: number;\n    include_stats?: boolean;\n  } = {}): Promise<PaginatedResponse<CategoryWithStats> | ApiResponse<CategoryWithStats[]>> => {\n    const response = await apiClient.get('/categories', { params });\n    return response.data;\n  },\n\n  // Get active categories (for dropdowns)\n  getActiveCategories: async (): Promise<Category[]> => {\n    const response = await apiClient.get<ApiResponse<Category[]>>('/categories/active');\n    return response.data.data;\n  },\n\n  // Get category by ID\n  getCategory: async (id: number, includeStats = false): Promise<CategoryWithStats> => {\n    const response = await apiClient.get<ApiResponse<CategoryWithStats>>(`/categories/${id}`, {\n      params: { include_stats: includeStats }\n    });\n    return response.data.data;\n  },\n\n  // Create new category (admin only)\n  createCategory: async (data: CreateCategoryRequest): Promise<Category> => {\n    const response = await apiClient.post<ApiResponse<Category>>('/admin/categories', data);\n    return response.data.data;\n  },\n\n  // Update category (admin only)\n  updateCategory: async (id: number, data: UpdateCategoryRequest): Promise<Category> => {\n    const response = await apiClient.put<ApiResponse<Category>>(`/admin/categories/${id}`, data);\n    return response.data.data;\n  },\n\n  // Delete category (admin only)\n  deleteCategory: async (id: number): Promise<void> => {\n    await apiClient.delete(`/admin/categories/${id}`);\n  },\n\n  // Get category statistics\n  getStats: async (): Promise<{ total_categories: number; active_categories: number }> => {\n    const response = await apiClient.get<ApiResponse<{ total_categories: number; active_categories: number }>>('/categories/stats');\n    return response.data.data;\n  },\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,UAAU;AAUhC,OAAO,MAAMC,aAAa,GAAG;EAC3B;EACAC,aAAa,EAAE,MAAAA,CAAOC,MAIrB,GAAG,CAAC,CAAC,KAAuF;IAC3F,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,aAAa,EAAE;MAAEF;IAAO,CAAC,CAAC;IAC/D,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,mBAAmB,EAAE,MAAAA,CAAA,KAAiC;IACpD,MAAMH,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAA0B,oBAAoB,CAAC;IACnF,OAAOD,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAE,WAAW,EAAE,MAAAA,CAAOC,EAAU,EAAEC,YAAY,GAAG,KAAK,KAAiC;IACnF,MAAMN,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAiC,eAAeI,EAAE,EAAE,EAAE;MACxFN,MAAM,EAAE;QAAEQ,aAAa,EAAED;MAAa;IACxC,CAAC,CAAC;IACF,OAAON,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAM,cAAc,EAAE,MAAON,IAA2B,IAAwB;IACxE,MAAMF,QAAQ,GAAG,MAAMJ,SAAS,CAACa,IAAI,CAAwB,mBAAmB,EAAEP,IAAI,CAAC;IACvF,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAQ,cAAc,EAAE,MAAAA,CAAOL,EAAU,EAAEH,IAA2B,KAAwB;IACpF,MAAMF,QAAQ,GAAG,MAAMJ,SAAS,CAACe,GAAG,CAAwB,qBAAqBN,EAAE,EAAE,EAAEH,IAAI,CAAC;IAC5F,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;EACAU,cAAc,EAAE,MAAOP,EAAU,IAAoB;IACnD,MAAMT,SAAS,CAACiB,MAAM,CAAC,qBAAqBR,EAAE,EAAE,CAAC;EACnD,CAAC;EAED;EACAS,QAAQ,EAAE,MAAAA,CAAA,KAA8E;IACtF,MAAMd,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAuE,mBAAmB,CAAC;IAC/H,OAAOD,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}