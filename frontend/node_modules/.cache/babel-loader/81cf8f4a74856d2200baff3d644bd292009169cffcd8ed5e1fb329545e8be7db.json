{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/StatsCard.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = ({\n  title,\n  value,\n  icon,\n  color\n}) => {\n  const colorClasses = {\n    indigo: 'bg-indigo-500',\n    green: 'bg-green-500',\n    yellow: 'bg-yellow-500',\n    red: 'bg-red-500'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-white rounded-lg shadow-md\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-500 uppercase\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-bold text-gray-800\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-3 text-white rounded-full ${colorClasses[color]}`,\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: `${icon} fa-lg`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsCard;\nexport default StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "StatsCard", "title", "value", "icon", "color", "colorClasses", "indigo", "green", "yellow", "red", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/StatsCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatsCardProps {\n  title: string;\n  value: string;\n  icon: string;\n  color: 'indigo' | 'green' | 'yellow' | 'red';\n}\n\nconst StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, color }) => {\n  const colorClasses = {\n    indigo: 'bg-indigo-500',\n    green: 'bg-green-500',\n    yellow: 'bg-yellow-500',\n    red: 'bg-red-500',\n  };\n\n  return (\n    <div className=\"p-6 bg-white rounded-lg shadow-md\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-500 uppercase\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-800\">{value}</p>\n        </div>\n        <div className={`p-3 text-white rounded-full ${colorClasses[color]}`}>\n          <i className={`${icon} fa-lg`}></i>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1B,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAC7E,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE,eAAe;IACvBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE;EACP,CAAC;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAC,mCAAmC;IAAAC,QAAA,eAChDZ,OAAA;MAAKW,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDZ,OAAA;QAAAY,QAAA,gBACEZ,OAAA;UAAGW,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAEV;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEhB,OAAA;UAAGW,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAET;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAE,+BAA+BL,YAAY,CAACD,KAAK,CAAC,EAAG;QAAAO,QAAA,eACnEZ,OAAA;UAAGW,SAAS,EAAE,GAAGP,IAAI;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GArBIhB,SAAmC;AAuBzC,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}