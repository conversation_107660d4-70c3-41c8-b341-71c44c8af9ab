{"ast": null, "code": "// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({\n    dispatch,\n    getState\n  }) => next => action => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport { thunk, withExtraArgument };", "map": {"version": 3, "names": ["createThunkMiddleware", "extraArgument", "middleware", "dispatch", "getState", "next", "action", "thunk", "withExtraArgument"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/node_modules/redux-thunk/dist/redux-thunk.mjs"], "sourcesContent": ["// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({ dispatch, getState }) => (next) => (action) => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport {\n  thunk,\n  withExtraArgument\n};\n"], "mappings": "AAAA;AACA,SAASA,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,MAAMC,UAAU,GAAGA,CAAC;IAAEC,QAAQ;IAAEC;EAAS,CAAC,KAAMC,IAAI,IAAMC,MAAM,IAAK;IACnE,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM,CAACH,QAAQ,EAAEC,QAAQ,EAAEH,aAAa,CAAC;IAClD;IACA,OAAOI,IAAI,CAACC,MAAM,CAAC;EACrB,CAAC;EACD,OAAOJ,UAAU;AACnB;AACA,IAAIK,KAAK,GAAGP,qBAAqB,CAAC,CAAC;AACnC,IAAIQ,iBAAiB,GAAGR,qBAAqB;AAC7C,SACEO,KAAK,EACLC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}