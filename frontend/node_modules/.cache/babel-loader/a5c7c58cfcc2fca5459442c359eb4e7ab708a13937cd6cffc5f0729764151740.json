{"ast": null, "code": "var _s = $RefreshSig$();\nimport { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch, useSelector } from 'react-redux';\nimport authReducer from './authSlice';\nimport videosReducer from './videosSlice';\nimport categoriesReducer from './categoriesSlice';\nimport uiReducer from './uiSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    videos: videosReducer,\n    categories: categoriesReducer,\n    ui: uiReducer\n  }\n});\n// Typed hooks\nexport const useAppDispatch = () => {\n  _s();\n  return useDispatch();\n};\n_s(useAppDispatch, \"jI3HA1r1Cumjdbu14H7G+TUj798=\", false, function () {\n  return [useDispatch];\n});\nexport const useAppSelector = useSelector;", "map": {"version": 3, "names": ["configureStore", "useDispatch", "useSelector", "authReducer", "videosReducer", "categoriesReducer", "uiReducer", "store", "reducer", "auth", "videos", "categories", "ui", "useAppDispatch", "_s", "useAppSelector"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';\nimport authReducer from './authSlice';\nimport videosReducer from './videosSlice';\nimport categoriesReducer from './categoriesSlice';\nimport uiReducer from './uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    videos: videosReducer,\n    categories: categoriesReducer,\n    ui: uiReducer,\n  },\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n\n// Typed hooks\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAA8B,aAAa;AAC5E,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,iBAAiB,MAAM,mBAAmB;AACjD,OAAOC,SAAS,MAAM,WAAW;AAEjC,OAAO,MAAMC,KAAK,GAAGP,cAAc,CAAC;EAClCQ,OAAO,EAAE;IACPC,IAAI,EAAEN,WAAW;IACjBO,MAAM,EAAEN,aAAa;IACrBO,UAAU,EAAEN,iBAAiB;IAC7BO,EAAE,EAAEN;EACN;AACF,CAAC,CAAC;AAKF;AACA,OAAO,MAAMO,cAAc,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMb,WAAW,CAAc,CAAC;AAAA;AAACa,EAAA,CAAlDD,cAAc;EAAA,QAASZ,WAAW;AAAA;AAC/C,OAAO,MAAMc,cAA+C,GAAGb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}