{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoPlayerModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayerModal = ({\n  video,\n  isOpen,\n  onClose\n}) => {\n  _s();\n  var _video$category;\n  const modalRef = useRef(null);\n  const videoRef = useRef(null);\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  useEffect(() => {\n    if (isOpen && videoRef.current) {\n      videoRef.current.focus();\n    }\n  }, [isOpen]);\n  const handleOverlayClick = e => {\n    if (e.target === modalRef.current) {\n      onClose();\n    }\n  };\n  if (!isOpen) return null;\n  const videoUrl = video.file_path ? `http://localhost:8080/${video.file_path}` : '';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: modalRef,\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full max-w-6xl mx-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"absolute -top-12 right-0 text-white hover:text-gray-300 text-2xl z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-times\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative bg-black rounded-lg overflow-hidden\",\n        style: {\n          paddingBottom: '56.25%',\n          height: 0\n        },\n        children: videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n          ref: videoRef,\n          className: \"absolute top-0 left-0 w-full h-full\",\n          controls: true,\n          autoPlay: true,\n          src: videoUrl,\n          children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 left-0 w-full h-full flex items-center justify-center text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle text-4xl mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Video file not available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-2\",\n          children: video.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), video.description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-300 text-sm\",\n          children: video.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2 text-sm text-gray-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-eye mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), video.views, \" views\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-tag mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), ((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.name) || 'Uncategorized']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-calendar mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), new Date(video.created_at).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayerModal, \"wNb1EYdR9o0AIcPmyOUfyrYBWt0=\");\n_c = VideoPlayerModal;\nexport default VideoPlayerModal;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayerModal\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "VideoPlayerModal", "video", "isOpen", "onClose", "_s", "_video$category", "modalRef", "videoRef", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "current", "focus", "handleOverlayClick", "target", "videoUrl", "file_path", "ref", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingBottom", "height", "controls", "autoPlay", "src", "title", "description", "views", "category", "name", "Date", "created_at", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoPlayerModal.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { VideoWithDetails } from '../../types';\n\ninterface VideoPlayerModalProps {\n  video: VideoWithDetails;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst VideoPlayerModal: React.FC<VideoPlayerModalProps> = ({ video, isOpen, onClose }) => {\n  const modalRef = useRef<HTMLDivElement>(null);\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  useEffect(() => {\n    if (isOpen && videoRef.current) {\n      videoRef.current.focus();\n    }\n  }, [isOpen]);\n\n  const handleOverlayClick = (e: React.MouseEvent) => {\n    if (e.target === modalRef.current) {\n      onClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  const videoUrl = video.file_path ? `http://localhost:8080/${video.file_path}` : '';\n\n  return (\n    <div\n      ref={modalRef}\n      className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\"\n      onClick={handleOverlayClick}\n    >\n      <div className=\"relative w-full max-w-6xl mx-4\">\n        {/* Close Button */}\n        <button\n          onClick={onClose}\n          className=\"absolute -top-12 right-0 text-white hover:text-gray-300 text-2xl z-10\"\n        >\n          <i className=\"fas fa-times\"></i>\n        </button>\n\n        {/* Video Container */}\n        <div className=\"relative bg-black rounded-lg overflow-hidden\" style={{ paddingBottom: '56.25%', height: 0 }}>\n          {videoUrl ? (\n            <video\n              ref={videoRef}\n              className=\"absolute top-0 left-0 w-full h-full\"\n              controls\n              autoPlay\n              src={videoUrl}\n            >\n              Your browser does not support the video tag.\n            </video>\n          ) : (\n            <div className=\"absolute top-0 left-0 w-full h-full flex items-center justify-center text-white\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-exclamation-triangle text-4xl mb-4\"></i>\n                <p>Video file not available</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Video Info */}\n        <div className=\"mt-4 text-white\">\n          <h2 className=\"text-xl font-bold mb-2\">{video.title}</h2>\n          {video.description && (\n            <p className=\"text-gray-300 text-sm\">{video.description}</p>\n          )}\n          <div className=\"flex items-center mt-2 text-sm text-gray-400\">\n            <span className=\"mr-4\">\n              <i className=\"fas fa-eye mr-1\"></i>\n              {video.views} views\n            </span>\n            <span className=\"mr-4\">\n              <i className=\"fas fa-tag mr-1\"></i>\n              {video.category?.name || 'Uncategorized'}\n            </span>\n            <span>\n              <i className=\"fas fa-calendar mr-1\"></i>\n              {new Date(video.created_at).toLocaleDateString()}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlayerModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASjD,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACxF,MAAMC,QAAQ,GAAGT,MAAM,CAAiB,IAAI,CAAC;EAC7C,MAAMU,QAAQ,GAAGV,MAAM,CAAmB,IAAI,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd,MAAMY,YAAY,GAAIC,CAAgB,IAAK;MACzC,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBP,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVS,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErBP,SAAS,CAAC,MAAM;IACd,IAAIM,MAAM,IAAIK,QAAQ,CAACU,OAAO,EAAE;MAC9BV,QAAQ,CAACU,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAChB,MAAM,CAAC,CAAC;EAEZ,MAAMiB,kBAAkB,GAAIV,CAAmB,IAAK;IAClD,IAAIA,CAAC,CAACW,MAAM,KAAKd,QAAQ,CAACW,OAAO,EAAE;MACjCd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMmB,QAAQ,GAAGpB,KAAK,CAACqB,SAAS,GAAG,yBAAyBrB,KAAK,CAACqB,SAAS,EAAE,GAAG,EAAE;EAElF,oBACEvB,OAAA;IACEwB,GAAG,EAAEjB,QAAS;IACdkB,SAAS,EAAC,4EAA4E;IACtFC,OAAO,EAAEN,kBAAmB;IAAAO,QAAA,eAE5B3B,OAAA;MAAKyB,SAAS,EAAC,gCAAgC;MAAAE,QAAA,gBAE7C3B,OAAA;QACE0B,OAAO,EAAEtB,OAAQ;QACjBqB,SAAS,EAAC,uEAAuE;QAAAE,QAAA,eAEjF3B,OAAA;UAAGyB,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGT/B,OAAA;QAAKyB,SAAS,EAAC,8CAA8C;QAACV,KAAK,EAAE;UAAEiB,aAAa,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAN,QAAA,EACzGL,QAAQ,gBACPtB,OAAA;UACEwB,GAAG,EAAEhB,QAAS;UACdiB,SAAS,EAAC,qCAAqC;UAC/CS,QAAQ;UACRC,QAAQ;UACRC,GAAG,EAAEd,QAAS;UAAAK,QAAA,EACf;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER/B,OAAA;UAAKyB,SAAS,EAAC,iFAAiF;UAAAE,QAAA,eAC9F3B,OAAA;YAAKyB,SAAS,EAAC,aAAa;YAAAE,QAAA,gBAC1B3B,OAAA;cAAGyB,SAAS,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D/B,OAAA;cAAA2B,QAAA,EAAG;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAE,QAAA,gBAC9B3B,OAAA;UAAIyB,SAAS,EAAC,wBAAwB;UAAAE,QAAA,EAAEzB,KAAK,CAACmC;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACxD7B,KAAK,CAACoC,WAAW,iBAChBtC,OAAA;UAAGyB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAEzB,KAAK,CAACoC;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC5D,eACD/B,OAAA;UAAKyB,SAAS,EAAC,8CAA8C;UAAAE,QAAA,gBAC3D3B,OAAA;YAAMyB,SAAS,EAAC,MAAM;YAAAE,QAAA,gBACpB3B,OAAA;cAAGyB,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClC7B,KAAK,CAACqC,KAAK,EAAC,QACf;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/B,OAAA;YAAMyB,SAAS,EAAC,MAAM;YAAAE,QAAA,gBACpB3B,OAAA;cAAGyB,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClC,EAAAzB,eAAA,GAAAJ,KAAK,CAACsC,QAAQ,cAAAlC,eAAA,uBAAdA,eAAA,CAAgBmC,IAAI,KAAI,eAAe;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACP/B,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAGyB,SAAS,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvC,IAAIW,IAAI,CAACxC,KAAK,CAACyC,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAnGIJ,gBAAiD;AAAA4C,EAAA,GAAjD5C,gBAAiD;AAqGvD,eAAeA,gBAAgB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}