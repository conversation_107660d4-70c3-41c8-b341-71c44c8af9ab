{"program": {"fileNames": ["../typescript/lib/lib.es5.d.ts", "../typescript/lib/lib.es2015.d.ts", "../typescript/lib/lib.es2016.d.ts", "../typescript/lib/lib.es2017.d.ts", "../typescript/lib/lib.es2018.d.ts", "../typescript/lib/lib.es2019.d.ts", "../typescript/lib/lib.es2020.d.ts", "../typescript/lib/lib.es2021.d.ts", "../typescript/lib/lib.es2022.d.ts", "../typescript/lib/lib.esnext.d.ts", "../typescript/lib/lib.dom.d.ts", "../typescript/lib/lib.dom.iterable.d.ts", "../typescript/lib/lib.es2015.core.d.ts", "../typescript/lib/lib.es2015.collection.d.ts", "../typescript/lib/lib.es2015.generator.d.ts", "../typescript/lib/lib.es2015.iterable.d.ts", "../typescript/lib/lib.es2015.promise.d.ts", "../typescript/lib/lib.es2015.proxy.d.ts", "../typescript/lib/lib.es2015.reflect.d.ts", "../typescript/lib/lib.es2015.symbol.d.ts", "../typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../typescript/lib/lib.es2016.array.include.d.ts", "../typescript/lib/lib.es2017.object.d.ts", "../typescript/lib/lib.es2017.sharedmemory.d.ts", "../typescript/lib/lib.es2017.string.d.ts", "../typescript/lib/lib.es2017.intl.d.ts", "../typescript/lib/lib.es2017.typedarrays.d.ts", "../typescript/lib/lib.es2018.asyncgenerator.d.ts", "../typescript/lib/lib.es2018.asynciterable.d.ts", "../typescript/lib/lib.es2018.intl.d.ts", "../typescript/lib/lib.es2018.promise.d.ts", "../typescript/lib/lib.es2018.regexp.d.ts", "../typescript/lib/lib.es2019.array.d.ts", "../typescript/lib/lib.es2019.object.d.ts", "../typescript/lib/lib.es2019.string.d.ts", "../typescript/lib/lib.es2019.symbol.d.ts", "../typescript/lib/lib.es2019.intl.d.ts", "../typescript/lib/lib.es2020.bigint.d.ts", "../typescript/lib/lib.es2020.date.d.ts", "../typescript/lib/lib.es2020.promise.d.ts", "../typescript/lib/lib.es2020.sharedmemory.d.ts", "../typescript/lib/lib.es2020.string.d.ts", "../typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../typescript/lib/lib.es2020.intl.d.ts", "../typescript/lib/lib.es2020.number.d.ts", "../typescript/lib/lib.es2021.promise.d.ts", "../typescript/lib/lib.es2021.string.d.ts", "../typescript/lib/lib.es2021.weakref.d.ts", "../typescript/lib/lib.es2021.intl.d.ts", "../typescript/lib/lib.es2022.array.d.ts", "../typescript/lib/lib.es2022.error.d.ts", "../typescript/lib/lib.es2022.intl.d.ts", "../typescript/lib/lib.es2022.object.d.ts", "../typescript/lib/lib.es2022.sharedmemory.d.ts", "../typescript/lib/lib.es2022.string.d.ts", "../typescript/lib/lib.esnext.intl.d.ts", "../@types/react/ts5.0/global.d.ts", "../csstype/index.d.ts", "../@types/react/ts5.0/index.d.ts", "../@types/react/ts5.0/jsx-runtime.d.ts", "../@types/react-dom/client.d.ts", "../@types/aria-query/index.d.ts", "../@testing-library/dom/types/matches.d.ts", "../@testing-library/dom/types/wait-for.d.ts", "../@testing-library/dom/types/query-helpers.d.ts", "../@testing-library/dom/types/queries.d.ts", "../@testing-library/dom/types/get-queries-for-element.d.ts", "../pretty-format/build/types.d.ts", "../pretty-format/build/index.d.ts", "../@testing-library/dom/types/screen.d.ts", "../@testing-library/dom/types/wait-for-element-to-be-removed.d.ts", "../@testing-library/dom/types/get-node-text.d.ts", "../@testing-library/dom/types/events.d.ts", "../@testing-library/dom/types/pretty-dom.d.ts", "../@testing-library/dom/types/role-helpers.d.ts", "../@testing-library/dom/types/config.d.ts", "../@testing-library/dom/types/suggestions.d.ts", "../@testing-library/dom/types/index.d.ts", "../@types/react-dom/test-utils/index.d.ts", "../@testing-library/react/types/index.d.ts", "../redux/dist/redux.d.ts", "../react-redux/dist/react-redux.d.ts", "../@reduxjs/toolkit/node_modules/immer/dist/immer.d.ts", "../reselect/dist/reselect.d.ts", "../redux-thunk/dist/redux-thunk.d.ts", "../@reduxjs/toolkit/dist/uncheckedindexed.ts", "../@reduxjs/toolkit/dist/index.d.ts", "../../src/types/index.ts", "../axios/index.d.ts", "../../src/api/client.ts", "../../src/api/auth.ts", "../../src/store/authSlice.ts", "../../src/api/videos.ts", "../../src/store/videosSlice.ts", "../../src/api/categories.ts", "../../src/store/categoriesSlice.ts", "../../src/store/uiSlice.ts", "../../src/store/index.ts", "../../src/components/Layout/Sidebar.tsx", "../../src/components/Layout/Header.tsx", "../../src/components/Dashboard/StatsCard.tsx", "../../src/components/Dashboard/RecentVideos.tsx", "../../src/components/Dashboard/Dashboard.tsx", "../../src/components/Videos/VideoCard.tsx", "../../src/components/Videos/VideoPlayerModal.tsx", "../../src/components/Common/Pagination.tsx", "../../src/components/Videos/VideoGrid.tsx", "../../src/components/Videos/VideoFilters.tsx", "../../src/components/Videos/UploadVideoModal.tsx", "../../src/components/Videos/VideoManagement.tsx", "../../src/components/Users/<USER>", "../../src/components/Categories/CategoryModal.tsx", "../../src/components/Categories/CategoryManagement.tsx", "../../src/components/Settings/Settings.tsx", "../../src/components/Layout/Layout.tsx", "../../src/components/Auth/Login.tsx", "../../src/App.tsx", "../../src/App.test.tsx", "../web-vitals/dist/modules/types.d.ts", "../web-vitals/dist/modules/getCLS.d.ts", "../web-vitals/dist/modules/getFCP.d.ts", "../web-vitals/dist/modules/getFID.d.ts", "../web-vitals/dist/modules/getLCP.d.ts", "../web-vitals/dist/modules/getTTFB.d.ts", "../web-vitals/dist/modules/index.d.ts", "../../src/reportWebVitals.ts", "../../src/index.tsx", "../@types/node/compatibility/indexable.d.ts", "../@types/node/compatibility/iterators.d.ts", "../@types/node/compatibility/index.d.ts", "../@types/node/ts5.6/globals.typedarray.d.ts", "../@types/node/ts5.6/buffer.buffer.d.ts", "../@types/node/globals.d.ts", "../@types/node/assert.d.ts", "../@types/node/assert/strict.d.ts", "../@types/node/async_hooks.d.ts", "../@types/node/buffer.d.ts", "../@types/node/child_process.d.ts", "../@types/node/cluster.d.ts", "../@types/node/console.d.ts", "../@types/node/constants.d.ts", "../@types/node/crypto.d.ts", "../@types/node/dgram.d.ts", "../@types/node/diagnostics_channel.d.ts", "../@types/node/dns.d.ts", "../@types/node/dns/promises.d.ts", "../@types/node/dom-events.d.ts", "../@types/node/domain.d.ts", "../@types/node/events.d.ts", "../@types/node/fs.d.ts", "../@types/node/fs/promises.d.ts", "../@types/node/http.d.ts", "../@types/node/http2.d.ts", "../@types/node/https.d.ts", "../@types/node/inspector.d.ts", "../@types/node/module.d.ts", "../@types/node/net.d.ts", "../@types/node/os.d.ts", "../@types/node/path.d.ts", "../@types/node/perf_hooks.d.ts", "../@types/node/process.d.ts", "../@types/node/punycode.d.ts", "../@types/node/querystring.d.ts", "../@types/node/readline.d.ts", "../@types/node/repl.d.ts", "../@types/node/stream.d.ts", "../@types/node/stream/promises.d.ts", "../@types/node/stream/consumers.d.ts", "../@types/node/stream/web.d.ts", "../@types/node/string_decoder.d.ts", "../@types/node/test.d.ts", "../@types/node/timers.d.ts", "../@types/node/timers/promises.d.ts", "../@types/node/tls.d.ts", "../@types/node/trace_events.d.ts", "../@types/node/tty.d.ts", "../@types/node/url.d.ts", "../@types/node/util.d.ts", "../@types/node/v8.d.ts", "../@types/node/vm.d.ts", "../@types/node/wasi.d.ts", "../@types/node/worker_threads.d.ts", "../@types/node/zlib.d.ts", "../@types/node/ts5.6/index.d.ts", "../@types/react-dom/index.d.ts", "../react-scripts/lib/react-app.d.ts", "../../src/react-app-env.d.ts", "../chalk/index.d.ts", "../jest-diff/build/cleanupSemantic.d.ts", "../jest-diff/build/types.d.ts", "../jest-diff/build/diffLines.d.ts", "../jest-diff/build/printDiffs.d.ts", "../jest-diff/build/index.d.ts", "../jest-matcher-utils/build/index.d.ts", "../@types/jest/index.d.ts", "../@testing-library/jest-dom/types/matchers.d.ts", "../@testing-library/jest-dom/types/jest.d.ts", "../@testing-library/jest-dom/types/index.d.ts", "../../src/setupTests.ts", "../../src/api/users.ts", "../@babel/types/lib/index.d.ts", "../@types/babel__generator/index.d.ts", "../@babel/parser/typings/babel-parser.d.ts", "../@types/babel__template/index.d.ts", "../@types/babel__traverse/index.d.ts", "../@types/babel__core/index.d.ts", "../@types/connect/index.d.ts", "../@types/body-parser/index.d.ts", "../@types/bonjour/index.d.ts", "../@types/mime/index.d.ts", "../@types/send/index.d.ts", "../@types/qs/index.d.ts", "../@types/range-parser/index.d.ts", "../@types/express-serve-static-core/index.d.ts", "../@types/connect-history-api-fallback/index.d.ts", "../@types/eslint/helpers.d.ts", "../@types/estree/index.d.ts", "../@types/json-schema/index.d.ts", "../@types/eslint/index.d.ts", "../@types/eslint-scope/index.d.ts", "../@types/http-errors/index.d.ts", "../@types/serve-static/index.d.ts", "../@types/express/node_modules/@types/express-serve-static-core/index.d.ts", "../@types/express/index.d.ts", "../@types/graceful-fs/index.d.ts", "../@types/history/DOMUtils.d.ts", "../@types/history/createBrowserHistory.d.ts", "../@types/history/createHashHistory.d.ts", "../@types/history/createMemoryHistory.d.ts", "../@types/history/LocationUtils.d.ts", "../@types/history/PathUtils.d.ts", "../@types/history/index.d.ts", "../@types/html-minifier-terser/index.d.ts", "../@types/http-proxy/index.d.ts", "../@types/istanbul-lib-coverage/index.d.ts", "../@types/istanbul-lib-report/index.d.ts", "../@types/istanbul-reports/index.d.ts", "../@types/json5/index.d.ts", "../@types/node-forge/index.d.ts", "../@types/parse-json/index.d.ts", "../@types/prettier/index.d.ts", "../@types/q/index.d.ts", "../@types/react-router/index.d.ts", "../react-router/dist/development/routeModules-BR2FO0ix.d.ts", "../react-router/dist/development/index-react-server-client-Bi_fx8qz.d.ts", "../react-router/node_modules/cookie/dist/index.d.ts", "../react-router/dist/development/register-DiOIlEq5.d.ts", "../react-router/dist/development/index.d.ts", "../@types/react-router-dom/index.d.ts", "../@types/resolve/index.d.ts", "../@types/retry/index.d.ts", "../@types/semver/classes/semver.d.ts", "../@types/semver/functions/parse.d.ts", "../@types/semver/functions/valid.d.ts", "../@types/semver/functions/clean.d.ts", "../@types/semver/functions/inc.d.ts", "../@types/semver/functions/diff.d.ts", "../@types/semver/functions/major.d.ts", "../@types/semver/functions/minor.d.ts", "../@types/semver/functions/patch.d.ts", "../@types/semver/functions/prerelease.d.ts", "../@types/semver/functions/compare.d.ts", "../@types/semver/functions/rcompare.d.ts", "../@types/semver/functions/compare-loose.d.ts", "../@types/semver/functions/compare-build.d.ts", "../@types/semver/functions/sort.d.ts", "../@types/semver/functions/rsort.d.ts", "../@types/semver/functions/gt.d.ts", "../@types/semver/functions/lt.d.ts", "../@types/semver/functions/eq.d.ts", "../@types/semver/functions/neq.d.ts", "../@types/semver/functions/gte.d.ts", "../@types/semver/functions/lte.d.ts", "../@types/semver/functions/cmp.d.ts", "../@types/semver/functions/coerce.d.ts", "../@types/semver/classes/comparator.d.ts", "../@types/semver/classes/range.d.ts", "../@types/semver/functions/satisfies.d.ts", "../@types/semver/ranges/max-satisfying.d.ts", "../@types/semver/ranges/min-satisfying.d.ts", "../@types/semver/ranges/to-comparators.d.ts", "../@types/semver/ranges/min-version.d.ts", "../@types/semver/ranges/valid.d.ts", "../@types/semver/ranges/outside.d.ts", "../@types/semver/ranges/gtr.d.ts", "../@types/semver/ranges/ltr.d.ts", "../@types/semver/ranges/intersects.d.ts", "../@types/semver/ranges/simplify.d.ts", "../@types/semver/ranges/subset.d.ts", "../@types/semver/internals/identifiers.d.ts", "../@types/semver/index.d.ts", "../@types/serve-index/index.d.ts", "../@types/sockjs/index.d.ts", "../@types/stack-utils/index.d.ts", "../@types/trusted-types/lib/index.d.ts", "../@types/trusted-types/index.d.ts", "../@types/use-sync-external-store/index.d.ts", "../@types/ws/index.d.ts", "../@types/yargs-parser/index.d.ts", "../@types/yargs/index.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "4b421cbfb3a38a27c279dec1e9112c3d1da296f77a1a85ddadf7e7a425d45d18", "1fc5ab7a764205c68fa10d381b08417795fc73111d6dd16b5b1ed36badb743d9", "746d62152361558ea6d6115cf0da4dd10ede041d14882ede3568bce5dc4b4f1f", "d11a03592451da2d1065e09e61f4e2a9bf68f780f4f6623c18b57816a9679d17", "aea179452def8a6152f98f63b191b84e7cbd69b0e248c91e61fb2e52328abe8c", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "5e5e095c4470c8bab227dbbc61374878ecead104c74ab9960d3adcccfee23205", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "d7f680a43f8cd12a6b6122c07c54ba40952b0c8aa140dcfcf32eb9e6cb028596", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "b248e32ca52e8f5571390a4142558ae4f203ae2f94d5bac38a3084d529ef4e58", "affectsGlobalScope": true}, {"version": "6c55633c733c8378db65ac3da7a767c3cf2cf3057f0565a9124a16a3a2019e87", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "34c839eaaa6d78c8674ae2c37af2236dee6831b13db7b4ef4df3ec889a04d4f2", "affectsGlobalScope": true}, {"version": "34478567f8a80171f88f2f30808beb7da15eac0538ae91282dd33dce928d98ed", "affectsGlobalScope": true}, {"version": "ab7d58e6161a550ff92e5aff755dc37fe896245348332cd5f1e1203479fe0ed1", "affectsGlobalScope": true}, {"version": "6bda95ea27a59a276e46043b7065b55bd4b316c25e70e29b572958fa77565d43", "affectsGlobalScope": true}, {"version": "aedb8de1abb2ff1095c153854a6df7deae4a5709c37297f9d6e9948b6806fa66", "affectsGlobalScope": true}, {"version": "a4da0551fd39b90ca7ce5f68fb55d4dc0c1396d589b612e1902f68ee090aaada", "affectsGlobalScope": true}, {"version": "11ffe3c281f375fff9ffdde8bbec7669b4dd671905509079f866f2354a788064", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, {"version": "170d4db14678c68178ee8a3d5a990d5afb759ecb6ec44dbd885c50f6da6204f6", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "2d27d86432b871be0d83c0988a42ae71a70aa516ac3e0944c296708aaaa24c63", "016fe1e807dfdb88e8773616dde55bd04c087675662a393f20b1ec213b4a2b74", "88e9caa9c5d2ba629240b5913842e7c57c5c0315383b8dc9d436ef2b60f1c391", "ae77d81a5541a8abb938a0efedf9ac4bea36fb3a24cc28cfa11c598863aba571", "3cfb7c0c642b19fb75132154040bb7cd840f0002f9955b14154e69611b9b3f81", "8387ec1601cf6b8948672537cf8d430431ba0d87b1f9537b4597c1ab8d3ade5b", "d16f1c460b1ca9158e030fdf3641e1de11135e0c7169d3e8cf17cc4cc35d5e64", "a934063af84f8117b8ce51851c1af2b76efe960aa4c7b48d0343a1b15c01aedf", "e3c5ad476eb2fca8505aee5bdfdf9bf11760df5d0f9545db23f12a5c4d72a718", "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "d0570ce419fb38287e7b39c910b468becb5b2278cf33b1000a3d3e82a46ecae2", "3aca7f4260dad9dcc0a0333654cb3cde6664d34a553ec06c953bce11151764d7", "a0a6f0095f25f08a7129bc4d7cb8438039ec422dc341218d274e1e5131115988", "b58f396fe4cfe5a0e4d594996bc8c1bfe25496fbc66cf169d41ac3c139418c77", "45785e608b3d380c79e21957a6d1467e1206ac0281644e43e8ed6498808ace72", "bece27602416508ba946868ad34d09997911016dbd6893fb884633017f74e2c5", "2a90177ebaef25de89351de964c2c601ab54d6e3a157cba60d9cd3eaf5a5ee1a", "82200e963d3c767976a5a9f41ecf8c65eca14a6b33dcbe00214fcbe959698c46", "b4966c503c08bbd9e834037a8ab60e5f53c5fd1092e8873c4a1c344806acdab2", "3d3208d0f061e4836dd5f144425781c172987c430f7eaee483fadaa3c5780f9f", "480c20eddc2ee5f57954609b2f7a3368f6e0dda4037aa09ccf0d37e0b20d4e5c", {"version": "f734b58ea162765ff4d4a36f671ee06da898921e985a2064510f4925ec1ed062", "affectsGlobalScope": true}, "9b643d11b5bca11af760795e56096beae0ed29e9027fec409481f2ee1cb54bbc", "07cbc706c24fa086bcc20daee910b9afa5dc5294e14771355861686c9d5235fd", "37f96daaddc2dd96712b2e86f3901f477ac01a5c2539b1bc07fd609d62039ee1", "9c5c84c449a3d74e417343410ba9f1bd8bfeb32abd16945a1b3d0592ded31bc8", "c0bd5112f5e51ab7dfa8660cdd22af3b4385a682f33eefde2a1be35b60d57eb1", "be5bb7b563c09119bd9f32b3490ab988852ffe10d4016087c094a80ddf6a0e28", {"version": "f368c818a85e30f0d503924f1c8615babaed1228994460043b44266fc7dda907", "signature": "98f89c91e883413a03dfc232f5edf6ed5789df4754b77fdceecd05372f8cba71"}, "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "7281282c8dbf85f557fd435cc8f0030a58d6f0de6bd70aa595b8c00f48bac173", "bcb3a110cc90cb2c74a7fd5fd14a4c1d79323794a5b780fb8c40db49b1b033e5", "4fc6ef609c4822a26e86ae5d46c92f50d8d23da0ad0606681e3f782617eb4c2d", "b9013f5c12dade34684c5237663012547c955a7c21d8916a1310e8f300824c18", {"version": "3cdc9483eb063fda2d55ee787ff06c9bfd19c81492a3692cfd9a59bbf401be60", "signature": "7946f6accafe9018d8f7d51ada9f024ee5d597e8f593dc23bce4b122eec9470f"}, "58ba3caf7df4963edf5c89b72c2304da1e159c9490bcad0fc6a98aa6b70c8952", {"version": "e7c70c1c17cc47d372aabd4a21ea722c78d93f724f4ddf9c8bf490a466eacae7", "signature": "a6c79e71cb04070bda58b8b64be5872bbdc35e97d4dc956e62ddffa2593d5175"}, "228cd2e111e29f075eb74d35d53654cc1e664aeedbbfccf466932fd3439a97ef", "b1cfd3475b59f75f6c70f91d7f25d88f9fd3fe4d9b01438c399a5dbf7bd86b9e", "cdc35eb05711b6646603b54b0b108ef9119e1dfc31bf30eadf99a674d9cf3c70", "2f22d007951e97757d1e2aba01a809ff1ccc192339d172d89ed4aa30e69e2f00", "cc61a1ec405f41369ecec7d0f34a3e626c6e5122d471867d7042e3806bd05a78", "9ab9494eb252b42e1c5597343b14fc8802cefdd4a0365796dfe2b22f953d44ab", "624cb95437e9c46c95e28a68efc8f957c28b5f48ff9737976b5cdcaa86560e26", "5977096c4c9fe6b6447e7e2a7e1537c87ff99ec4ffa19141e38d908bd284edf5", "1f6f38c0e9c8b3ed3a59bc63e5b0bc03a7aa34e883c0c0c93db9ddd8d69723c0", "4ba1bf7dab3b48ec6039d06207234e27faf3930d140a0b702d640dd1506a2a99", "d7b15c9b3cb3586f6fecdb5e2ae417b025fcebe2f7b0d9a0c741be9f5601ef3a", "ce2dd0b679979edbdd84f5601cd89b8c9b2c113461c537fb6a6c09d3e669852d", "764738ad0b5e01ca0a8b4bdb97e0f609d5c2f7621547bb4ff081d3fbed52e9cb", "db4c4bc587ee0fdad1a1096eaf507c53673aca52e073b95cde2a537a725ff724", "d7073004809e0c8068f1d7039444a0ca40df83d5b13808977aa2a8e3c2ca2009", "7fdc2c67406a572ce65512e16fb1615c9d2d64f6e2dd7ee13530f3f4362e94f3", "03586a5c0c2466b8bfc658a42aa41e5a3a9b06b4fdba96c0b05536e36115dda3", "ca62afec0ae62825b85857b26d2f53e7d2e6592ae6229e84909cec5f33a7a123", "69e5c019f96f67be378c3f9702f422c53f7de378cad13a01e1c288c9a89961f0", "17ee489fc92d7bdfc7ffdb61beb7516aa38e0c5ab9fca3b7c00b3556694ea4d0", "79d5cb6cd1b0678493321e5ac31180c58ef14af5d1cb87fe242f0c7e0ac8937e", "1f0914ca057e799130da87a78d48021657aba67e01fcbcb50b099944ee2ea864", {"version": "6231095d0b356894ceec8856ce4e354a38a773a62067978f3af9c478acbcf2e9", "affectsGlobalScope": true}, "66754e21a2ac3ffc0eb3fc55cf5c33deb2c115b726fb2c4368b870c5e85106ce", "ccd9f8f7c8760d8eb0c0c112c772a9756d59365a61fd5cc102a889fed79d0076", "d7ccbcf0e92d0209f499729768c791fbc278ecad02c3f596db5b519eb4f5d496", "85542ea0f889f8993e6b4631c541640c17c230bf5cf18d483184de9105536b4d", "8cc2eed7caa0e187fae67a1e7fdb4da79079489744c6e5be9e0839d258fd5995", "aaf2cc4016c33c836fcca0dbe20d7a97bf17cafeb581a57a62267b1c38930bd4", "fa208d5a5ab6d20b64122998b074707dea08d12ed619652955f77df958d85786", "3406039f2208d02e99c0c41c9e429c5b559df4a32f494b5bbea4ee9c99bb437a", {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true}, {"version": "437e20f2ba32abaeb7985e0afe0002de1917bc74e949ba585e49feba65da6ca1", "affectsGlobalScope": true}, "2e864ea827318e5f490863a8cd412744d9ddb175acf488dd02a941703dad1e38", {"version": "613b21ccdf3be6329d56e6caa13b258c842edf8377be7bc9f014ed14cdcfc308", "affectsGlobalScope": true}, {"version": "894dae169f8193e3f07c3fec14149a60592d1f13720907ffdf7b0c05cfb62c38", "affectsGlobalScope": true}, {"version": "df01885cc27c14632a8c38bdeb053295e69209107bb6c53988b78db5f450cb3c", "affectsGlobalScope": true}, "38379fa748cc5d259c96da356a849bd290a159ae218e06ec1daa166850e4bf50", "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "f51b4042a3ac86f1f707500a9768f88d0b0c1fc3f3e45a73333283dea720cdc6", {"version": "a29bc8aa8cc100d0c09370c03508f1245853efe017bb98699d4c690868371fc7", "affectsGlobalScope": true}, "6f95830ca11e2c7e82235b73dc149e68a0632b41e671724d12adc83a6750746d", "7aa011cda7cf0b9e87c85d128b2eeac9930bda215b0fee265d8bf2cec039fb5f", {"version": "92ec1aeca4e94bdab04083daa6039f807c0fce8f09bc42e8b24bf49fa5cdbbff", "affectsGlobalScope": true}, "a40826e8476694e90da94aa008283a7de50d1dafd37beada623863f1901cb7fb", "8463ab6a156dc96200b3d8b8a52dc8d878f13a6b7404439aa2f911d568132808", "5289750c112b5dd0e29dfa9089ddbf5d3ed1b544d99731093881e6967f5af4d1", "7693b90b3075deaccafd5efb467bf9f2b747a3075be888652ef73e64396d8628", "bd01a987f0fcf2344a405e542ee681e420651eaff1222a5a6e0c02fda52343bc", "693e50962e90a3548f41bff2c22676e3964212a836022d82e49eca0b20320a38", {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true}, "300b0c12998391154d7b9115a85554e91632a3d3e1b66038e98f2b9cb3c1061d", {"version": "222e742815b14d54db034788a7bee2d51197a2588c35b1fbefe04add6393021c", "affectsGlobalScope": true}, "93891e576a698609695e5b8117bb128336e4b7b28772e7d7e38e8075790eb42f", "69d90a2f13511eeaae271905c8615a93e20335530d1062a93cb04e754e5f04ad", "d723063c56101b34a7be5b28dbde80a3ae3dfd5e08fd49a3b569473337ead1f9", "fab49059d6c2026bdb2e53e4e5cde1a39da44e61daff1867c8b3b10b507bfe17", "5a551275f85bcc4003e543a1951a5b2f682cfba9b2922f65ae0df40ab71724a5", "22d1a3163b9a961dbe78b0aedbd7bcbc071ce1f31efb76eb013b0aee230fef0e", {"version": "c31695696ade4514cfcbb22799997b690d3dca7fb72beab68fb2e73b6ef450dd", "affectsGlobalScope": true}, "d99ad56d57f2c96daaf4475a8b64344b24dedafdb8f3c32d43552bcc72279a75", "a101ef17aece908c1029a1bd3f97132794dcff21b4ca0b997d9a633f962c46aa", "511575e18249b64b90d8f884fdb8a383c767d1a7efccd9d66a4e125a4dc5c462", {"version": "6d8001f2c3b86c4f1de1d45ecb3f87f287ed7313d6999f8c8318cec4f50e6323", "affectsGlobalScope": true}, {"version": "9e413bb587e01ba0cb1a87828cc9116669a4a71a61fe3a89b252f86f0c824bc2", "affectsGlobalScope": true}, "9c3d1222e6e3d8c35a4293d7a54d4142ebb8f7f70ec4111b8136df07fdc66169", "70173c475c6e76ccebc37412b02b2e26f62bf45fc1534c3ebe6d7fa60fb88819", "87ced739f77d80886ef2b923a7c52c363c549ad8799ae28eb8cc810892f511ad", "863bc4e31de6c75423bb02da16190d582b0a69b8964b61d45920e5b2cb3832dd", "849484324695b587f06abee7579641efe061b7338f9694ec410a76f477fe4df3", "269929a24b2816343a178008ac9ae9248304d92a8ba8e233055e0ed6dbe6ef71", "6e191fea1db6e9e4fa828259cf489e820ec9170effff57fb081a2f3295db4722", "49e0da63a2398d2ae88467f60a69b07e594b7777e01120cd9ebcefa1932484cf", "0435070b07e646b406b1c9b8b1b1878ea6917c32abc47e6435ec26d71212d513", "f71188f97c9f7d309798ec02a56dd3bf50a4e4d079b3480f275ac13719953898", {"version": "c4454589a0aa92c10d684c8c9584574bc404d1db556d72196cd31f8f7651af1a", "affectsGlobalScope": true}, "b17790866e140a630fa8891d7105c728a1bd60f4e35822e4b345af166a4a728c", "c50c75f4360f6fc06c4be29dafe28210e15c50cd6b04ad19c4808fa504efb51a", "d4a1f5f7ee89b2afffd3c74282f8ee65b24266c92b7d40398c12a27054ed745c", "900b5a9802192bc77eba35a5b87ce770df7b867a6d61772c554058c9ed635386", {"version": "d291d3d16fa252f6d460687491ea2c5c23098c9dc0d3e106b2803fdc98f48f29", "affectsGlobalScope": true}, {"version": "f43fcf89d75f13d0908a77cd3fa32b9fd28c915deded9b2778b08f2e242d07a7", "affectsGlobalScope": true}, "b9a616dec7430044ae735250f8d6a7183f5a9fba63f813e3d29dcab819fd7058", "aebf613f7831125038942eba891005fd25fa5cadcc3e3d13af4768dc7549161f", "0faee6b555890a1cb106e2adc5d3ffd89545b1da894d474e9d436596d654998f", "247e5c34784d185bc81442e8b1a371a36c4a5307a766a3725454c0a191b5cfad", "1c382a6446d63340be549a616ff5142a91653cea45d6d137e25b929130a4f29a", "729ad315d8fa8556a1cbf88604ce9bfd73f4cc2459b0b9f6da00f75150c2bf9d", "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", {"version": "9c52d1e0414faa6ee331024f249f9c1ab11a5c432c37370c2c74ba933aee25fc", "affectsGlobalScope": true}, "57eda4c4c04a1dca45c62857326882ce9cc948c4b52973c0e3c3b7e4c3fa3990", "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "d8aab31ba8e618cc3eea10b0945de81cb93b7e8150a013a482332263b9305322", "7adecb2c3238794c378d336a8182d4c3dd2c4fa6fa1785e2797a3db550edea62", "dc12dc0e5aa06f4e1a7692149b78f89116af823b9e1f1e4eae140cd3e0e674e6", "1bfc6565b90c8771615cd8cfcf9b36efc0275e5e83ac7d9181307e96eb495161", "8a8a96898906f065f296665e411f51010b51372fa260d5373bf9f64356703190", "7f82ef88bdb67d9a850dd1c7cd2d690f33e0f0acd208e3c9eba086f3670d4f73", {"version": "ccfd8774cd9b929f63ff7dcf657977eb0652e3547f1fcac1b3a1dc5db22d4d58", "affectsGlobalScope": true}, "72e9425f1ba1eb7fd8122d08f48848a0d56de1cd4c7b51f26dc2612bd26c7241", {"version": "841784cfa9046a2b3e453d638ea5c3e53680eb8225a45db1c13813f6ea4095e5", "affectsGlobalScope": true}, "646ef1cff0ec3cf8e96adb1848357788f244b217345944c2be2942a62764b771", "22583759d0045fdf8d62c9db0aacba9fd8bddde79c671aa08c97dcfd4e930cc6", "083bbabe189f2b8a9473a61a36d075fc7bb4696523fa71088ad08a527522a705", "a28ac3e717907284b3910b8e9b3f9844a4e0b0a861bea7b923e5adf90f620330", "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "82e5a50e17833a10eb091923b7e429dc846d42f1c6161eb6beeb964288d98a15", "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", "f9e22729fa06ed20f8b1fe60670b7c74933fdfd44d869ddfb1919c15a5cf12fb", "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", {"version": "a8932b7a5ef936687cc5b2492b525e2ad5e7ed321becfea4a17d5a6c80f49e92", "affectsGlobalScope": true}, "689be50b735f145624c6f391042155ae2ff6b90a93bac11ca5712bc866f6010c", {"version": "64d4b35c5456adf258d2cf56c341e203a073253f229ef3208fc0d5020253b241", "affectsGlobalScope": true}, "151ff381ef9ff8da2da9b9663ebf657eac35c4c9a19183420c05728f31a6761d", "f3d8c757e148ad968f0d98697987db363070abada5f503da3c06aefd9d4248c1", "dd0c1b380ba3437adedef134b2e48869449b1db0b07b2a229069309ce7b9dd39", "1f68ab0e055994eb337b67aa87d2a15e0200951e9664959b3866ee6f6b11a0fe", "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true}, "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "afe73051ff6a03a9565cbd8ebb0e956ee3df5e913ad5c1ded64218aabfa3dcb5", {"version": "271cde49dfd9b398ccc91bb3aaa43854cf76f4d14e10fed91cbac649aa6cbc63", "affectsGlobalScope": true}, "2bcecd31f1b4281710c666843fc55133a0ee25b143e59f35f49c62e168123f4b", "a6273756fa05f794b64fe1aff45f4371d444f51ed0257f9364a8b25f3501915d", "9c4e644fe9bf08d93c93bd892705842189fe345163f8896849d5964d21b56b78", "25d91fb9ed77a828cc6c7a863236fb712dafcd52f816eec481bd0c1f589f4404", "4cd14cea22eed1bfb0dc76183e56989f897ac5b14c0e2a819e5162eafdcfe243", "8d32432f68ca4ce93ad717823976f2db2add94c70c19602bf87ee67fe51df48b", "ee65fe452abe1309389c5f50710f24114e08a302d40708101c4aa950a2a7d044", "63786b6f821dee19eb898afb385bd58f1846e6cba593a35edcf9631ace09ba25", "035a5df183489c2e22f3cf59fc1ed2b043d27f357eecc0eb8d8e840059d44245", "a4809f4d92317535e6b22b01019437030077a76fec1d93b9881c9ed4738fcc54", "5f53fa0bd22096d2a78533f94e02c899143b8f0f9891a46965294ee8b91a9434", "c5a14bdeb170e0e67fb4200c54e0e02fd0ec94aca894c212c9d43c2916891542", "a39f2a304ccc39e70914e9db08f971d23b862b6f0e34753fad86b895fe566533", "916be7d770b0ae0406be9486ac12eb9825f21514961dd050594c4b250617d5a8", "d88a5e779faf033be3d52142a04fbe1cb96009868e3bbdd296b2bc6c59e06c0e", "8b677e0b88f3c4501c6f3ec44d3ccad1c2ba08efd8faf714b9b631b5dba1421b", "1d4bc73751d6ec6285331d1ca378904f55d9e5e8aeaa69bc45b675c3df83e778", "41633359acafb2de777374506c55cd38807d79d563f21c31182b17cdf735915a", "3683e76bbcd07f1a17c822fc02c8f3afe5c560f3f31d775e7d513d681e4f7038", "5a7ebcf5fe8ac590dd03af1bbe426dfed639a3490fb1e5d6b934e45643b8ea1b", "b7e1119637195dffe2cf05b0807d5afff3d89d20e05c8aff85a003386013e9bd", {"version": "f80a4ac9342c569c41a3594c4914f6a3d2e50751fa7f81425542f0f00627ac83", "affectsGlobalScope": true}, "8017277c3843df85296d8730f9edf097d68d7d5f9bc9d8124fcacf17ecfd487e", "8a19491eba2108d5c333c249699f40aff05ad312c04a17504573b27d91f0aede", "199f9ead0daf25ae4c5632e3d1f42570af59685294a38123eef457407e13f365", "cf3d384d082b933d987c4e2fe7bfb8710adfd9dc8155190056ed6695a25a559e", "9871b7ee672bc16c78833bdab3052615834b08375cb144e4d2cba74473f4a589", "c863198dae89420f3c552b5a03da6ed6d0acfa3807a64772b895db624b0de707", "8b03a5e327d7db67112ebbc93b4f744133eda2c1743dbb0a990c61a8007823ef", "86c73f2ee1752bac8eeeece234fd05dfcf0637a4fbd8032e4f5f43102faa8eec", "42fad1f540271e35ca37cecda12c4ce2eef27f0f5cf0f8dd761d723c744d3159", "ff3743a5de32bee10906aff63d1de726f6a7fd6ee2da4b8229054dfa69de2c34", "83acd370f7f84f203e71ebba33ba61b7f1291ca027d7f9a662c6307d74e4ac22", "1445cec898f90bdd18b2949b9590b3c012f5b7e1804e6e329fb0fe053946d5ec", "0e5318ec2275d8da858b541920d9306650ae6ac8012f0e872fe66eb50321a669", "cf530297c3fb3a92ec9591dd4fa229d58b5981e45fe6702a0bd2bea53a5e59be", "c1f6f7d08d42148ddfe164d36d7aba91f467dbcb3caa715966ff95f55048b3a4", "f4e9bf9103191ef3b3612d3ec0044ca4044ca5be27711fe648ada06fad4bcc85", "0c1ee27b8f6a00097c2d6d91a21ee4d096ab52c1e28350f6362542b55380059a", "7677d5b0db9e020d3017720f853ba18f415219fb3a9597343b1b1012cfd699f7", "bc1c6bc119c1784b1a2be6d9c47addec0d83ef0d52c8fbe1f14a51b4dfffc675", "52cf2ce99c2a23de70225e252e9822a22b4e0adb82643ab0b710858810e00bf1", "770625067bb27a20b9826255a8d47b6b5b0a2d3dfcbd21f89904c731f671ba77", "d1ed6765f4d7906a05968fb5cd6d1db8afa14dbe512a4884e8ea5c0f5e142c80", "799c0f1b07c092626cf1efd71d459997635911bb5f7fc1196efe449bba87e965", "2a184e4462b9914a30b1b5c41cf80c6d3428f17b20d3afb711fff3f0644001fd", "9eabde32a3aa5d80de34af2c2206cdc3ee094c6504a8d0c2d6d20c7c179503cc", "397c8051b6cfcb48aa22656f0faca2553c5f56187262135162ee79d2b2f6c966", "a8ead142e0c87dcd5dc130eba1f8eeed506b08952d905c47621dc2f583b1bff9", "a02f10ea5f73130efca046429254a4e3c06b5475baecc8f7b99a0014731be8b3", "c2576a4083232b0e2d9bd06875dd43d371dee2e090325a9eac0133fd5650c1cb", "4c9a0564bb317349de6a24eb4efea8bb79898fa72ad63a1809165f5bd42970dd", "f40ac11d8859092d20f953aae14ba967282c3bb056431a37fced1866ec7a2681", "cc11e9e79d4746cc59e0e17473a59d6f104692fd0eeea1bdb2e206eabed83b03", "b444a410d34fb5e98aa5ee2b381362044f4884652e8bc8a11c8fe14bbd85518e", "c35808c1f5e16d2c571aa65067e3cb95afeff843b259ecfa2fc107a9519b5392", "14d5dc055143e941c8743c6a21fa459f961cbc3deedf1bfe47b11587ca4b3ef5", "a3ad4e1fc542751005267d50a6298e6765928c0c3a8dce1572f2ba6ca518661c", "f237e7c97a3a89f4591afd49ecb3bd8d14f51a1c4adc8fcae3430febedff5eb6", "3ffdfbec93b7aed71082af62b8c3e0cc71261cc68d796665faa1e91604fbae8f", "662201f943ed45b1ad600d03a90dffe20841e725203ced8b708c91fcd7f9379a", "c9ef74c64ed051ea5b958621e7fb853fe3b56e8787c1587aefc6ea988b3c7e79", "2462ccfac5f3375794b861abaa81da380f1bbd9401de59ffa43119a0b644253d", "34baf65cfee92f110d6653322e2120c2d368ee64b3c7981dff08ed105c4f19b0", "844ab83672160ca57a2a2ea46da4c64200d8c18d4ebb2087819649cad099ff0e", "ddef25f825320de051dcb0e62ffce621b41c67712b5b4105740c32fd83f4c449", "1b3dffaa4ca8e38ac434856843505af767a614d187fb3a5ef4fcebb023c355aa", "ab82804a14454734010dcdcd43f564ff7b0389bee4c5692eec76ff5b30d4cf66", "15fe687c59d62741b4494d5e623d497d55eb38966ecf5bea7f36e48fc3fbe15e", {"version": "2c3b8be03577c98530ef9cb1a76e2c812636a871f367e9edf4c5f3ce702b77f8", "affectsGlobalScope": true}, "7fa8d75d229eeaee235a801758d9c694e94405013fe77d5d1dd8e3201fc414f1", "1ba59c8bbeed2cb75b239bb12041582fa3e8ef32f8d0bd0ec802e38442d3f317", "bae8d023ef6b23df7da26f51cea44321f95817c190342a36882e93b80d07a960", "c3e5b75e1af87b8e67e12e21332e708f7eccee6aac6261cfe98ca36652cdcb53"], "options": {"allowSyntheticDefaultImports": true, "declarationMap": false, "esModuleInterop": true, "inlineSourceMap": false, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 1, "tsBuildInfoFile": "./tsconfig.tsbuildinfo"}, "fileIdsList": [[132, 137, 201], [132, 137], [81, 83, 84, 85, 86, 132, 137], [60, 132, 137], [66, 132, 137], [63, 64, 65, 66, 67, 70, 71, 72, 73, 74, 75, 76, 77, 132, 137], [62, 132, 137], [69, 132, 137], [63, 64, 65, 132, 137], [63, 64, 132, 137], [66, 67, 69, 132, 137], [64, 132, 137], [132, 137, 197], [132, 137, 195, 196], [59, 61, 78, 79, 132, 137], [132, 137, 201, 202, 203, 204, 205], [132, 137, 201, 203], [132, 137, 152, 184, 207], [132, 137, 143, 184], [132, 137, 177, 184, 214], [132, 137, 152, 184], [132, 137, 217, 219], [132, 137, 216, 217, 218], [132, 137, 149, 152, 184, 211, 212, 213], [132, 137, 208, 212, 214, 222, 223], [132, 137, 150, 184], [132, 137, 232], [132, 137, 226, 232], [132, 137, 227, 228, 229, 230, 231], [132, 137, 149, 152, 154, 157, 166, 177, 184], [132, 137, 235], [132, 137, 236], [69, 132, 137, 194], [132, 137, 184], [132, 134, 137], [132, 136, 137], [132, 137, 142, 169], [132, 137, 138, 149, 150, 157, 166, 177], [132, 137, 138, 139, 149, 157], [128, 129, 132, 137], [132, 137, 140, 178], [132, 137, 141, 142, 150, 158], [132, 137, 142, 166, 174], [132, 137, 143, 145, 149, 157], [132, 137, 144], [132, 137, 145, 146], [132, 137, 149], [132, 137, 148, 149], [132, 136, 137, 149], [132, 137, 149, 150, 151, 166, 177], [132, 137, 149, 150, 151, 166], [132, 137, 149, 152, 157, 166, 177], [132, 137, 149, 150, 152, 153, 157, 166, 174, 177], [132, 137, 152, 154, 166, 174, 177], [132, 137, 149, 155], [132, 137, 156, 177, 182], [132, 137, 145, 149, 157, 166], [132, 137, 158], [132, 137, 159], [132, 136, 137, 160], [132, 137, 161, 176, 182], [132, 137, 162], [132, 137, 163], [132, 137, 149, 164], [132, 137, 164, 165, 178, 180], [132, 137, 149, 166, 167, 168], [132, 137, 166, 168], [132, 137, 166, 167], [132, 137, 169], [132, 137, 170], [132, 137, 149, 172, 173], [132, 137, 172, 173], [132, 137, 142, 157, 166, 174], [132, 137, 175], [137], [130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183], [132, 137, 157, 176], [132, 137, 152, 163, 177], [132, 137, 142, 178], [132, 137, 166, 179], [132, 137, 180], [132, 137, 181], [132, 137, 142, 149, 151, 160, 166, 177, 180, 182], [132, 137, 166, 183], [59, 132, 137], [59, 132, 137, 232, 248], [59, 132, 137, 232], [57, 58, 132, 137], [132, 137, 252, 291], [132, 137, 252, 276, 291], [132, 137, 291], [132, 137, 252], [132, 137, 252, 277, 291], [132, 137, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290], [132, 137, 277, 291], [132, 137, 150, 166, 184, 210], [132, 137, 150, 224], [132, 137, 152, 184, 211, 221], [132, 137, 295], [132, 137, 149, 152, 154, 157, 166, 174, 177, 183, 184], [132, 137, 299], [132, 137, 189, 190], [132, 137, 189, 190, 191, 192], [132, 137, 188, 193], [68, 132, 137], [59, 81, 132, 137], [59, 132, 137, 244], [59, 132, 136, 137, 244, 245, 246, 247], [59, 132, 137, 184, 185], [81, 132, 137], [119, 132, 137], [119, 120, 121, 122, 123, 124, 132, 137], [59, 60, 80, 117, 132, 137], [59, 60, 82, 98, 115, 116, 132, 137], [60, 88, 90, 132, 137], [60, 89, 132, 137], [59, 60, 82, 92, 98, 132, 137], [59, 60, 82, 96, 98, 112, 132, 137], [59, 60, 82, 88, 96, 132, 137], [59, 60, 132, 137], [59, 60, 82, 94, 98, 101, 102, 132, 137], [59, 60, 82, 98, 132, 137], [59, 60, 82, 97, 132, 137], [59, 60, 82, 92, 98, 99, 100, 103, 110, 111, 113, 114, 132, 137], [59, 60, 82, 92, 97, 98, 132, 137], [59, 60, 82, 88, 94, 132, 137], [59, 60, 82, 88, 94, 98, 132, 137], [59, 60, 88, 132, 137], [59, 60, 88, 104, 105, 106, 132, 137], [59, 60, 82, 94, 96, 98, 107, 108, 109, 132, 137], [59, 60, 61, 117, 126, 132, 137], [132, 137, 186], [60, 125, 132, 137], [60, 87, 88, 91, 132, 137], [60, 87, 88, 95, 132, 137], [60, 82, 87, 92, 94, 96, 97, 132, 137], [60, 87, 88, 132, 137], [60, 87, 88, 93, 132, 137], [81, 85, 87, 88]], "referencedMap": [[203, 1], [201, 2], [87, 3], [86, 4], [83, 2], [76, 2], [73, 2], [72, 2], [67, 5], [78, 6], [63, 7], [74, 8], [66, 9], [65, 10], [75, 2], [70, 11], [77, 2], [71, 12], [64, 2], [198, 13], [197, 14], [196, 7], [80, 15], [62, 2], [206, 16], [202, 1], [204, 17], [205, 1], [208, 18], [209, 19], [215, 20], [207, 21], [220, 22], [216, 2], [219, 23], [217, 2], [214, 24], [224, 25], [223, 24], [225, 26], [226, 2], [230, 27], [231, 27], [227, 28], [228, 28], [229, 28], [232, 29], [233, 2], [221, 2], [234, 30], [235, 2], [236, 31], [237, 32], [195, 33], [218, 2], [238, 2], [210, 2], [239, 34], [134, 35], [135, 35], [136, 36], [137, 37], [138, 38], [139, 39], [130, 40], [128, 2], [129, 2], [140, 41], [141, 42], [142, 43], [143, 44], [144, 45], [145, 46], [146, 46], [147, 47], [148, 48], [149, 49], [150, 50], [151, 51], [133, 2], [152, 52], [153, 53], [154, 54], [155, 55], [156, 56], [157, 57], [158, 58], [159, 59], [160, 60], [161, 61], [162, 62], [163, 63], [164, 64], [165, 65], [166, 66], [168, 67], [167, 68], [169, 69], [170, 70], [171, 2], [172, 71], [173, 72], [174, 73], [175, 74], [132, 75], [131, 2], [184, 76], [176, 77], [177, 78], [178, 79], [179, 80], [180, 81], [181, 82], [182, 83], [183, 84], [240, 2], [241, 2], [242, 2], [212, 2], [213, 2], [61, 85], [185, 85], [79, 85], [249, 86], [243, 87], [57, 2], [59, 88], [60, 85], [250, 34], [251, 2], [276, 89], [277, 90], [252, 91], [255, 91], [274, 89], [275, 89], [265, 89], [264, 92], [262, 89], [257, 89], [270, 89], [268, 89], [272, 89], [256, 89], [269, 89], [273, 89], [258, 89], [259, 89], [271, 89], [253, 89], [260, 89], [261, 89], [263, 89], [267, 89], [278, 93], [266, 89], [254, 89], [291, 94], [290, 2], [285, 93], [287, 95], [286, 93], [279, 93], [280, 93], [282, 93], [284, 93], [288, 95], [289, 95], [281, 95], [283, 95], [211, 96], [292, 97], [222, 98], [293, 21], [294, 2], [296, 99], [295, 2], [297, 2], [298, 100], [299, 2], [300, 101], [89, 2], [188, 2], [58, 2], [189, 2], [191, 102], [193, 103], [192, 102], [190, 8], [194, 104], [69, 105], [68, 2], [82, 106], [245, 107], [248, 108], [247, 2], [244, 85], [246, 2], [186, 109], [85, 110], [81, 2], [84, 2], [11, 2], [12, 2], [14, 2], [13, 2], [2, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [3, 2], [4, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [50, 2], [9, 2], [51, 2], [52, 2], [53, 2], [54, 2], [55, 2], [1, 2], [10, 2], [56, 2], [120, 111], [121, 111], [122, 111], [123, 111], [124, 111], [125, 112], [119, 2], [118, 113], [117, 114], [91, 115], [95, 115], [90, 116], [200, 115], [93, 115], [116, 117], [113, 118], [112, 119], [106, 120], [103, 121], [102, 122], [101, 120], [100, 123], [115, 124], [99, 125], [114, 120], [111, 122], [109, 126], [104, 127], [108, 128], [107, 129], [110, 130], [105, 128], [127, 131], [187, 132], [126, 133], [199, 4], [92, 134], [96, 135], [98, 136], [97, 137], [94, 138], [88, 4]], "exportedModulesMap": [[203, 1], [201, 2], [87, 3], [86, 4], [83, 2], [76, 2], [73, 2], [72, 2], [67, 5], [78, 6], [63, 7], [74, 8], [66, 9], [65, 10], [75, 2], [70, 11], [77, 2], [71, 12], [64, 2], [198, 13], [197, 14], [196, 7], [80, 15], [62, 2], [206, 16], [202, 1], [204, 17], [205, 1], [208, 18], [209, 19], [215, 20], [207, 21], [220, 22], [216, 2], [219, 23], [217, 2], [214, 24], [224, 25], [223, 24], [225, 26], [226, 2], [230, 27], [231, 27], [227, 28], [228, 28], [229, 28], [232, 29], [233, 2], [221, 2], [234, 30], [235, 2], [236, 31], [237, 32], [195, 33], [218, 2], [238, 2], [210, 2], [239, 34], [134, 35], [135, 35], [136, 36], [137, 37], [138, 38], [139, 39], [130, 40], [128, 2], [129, 2], [140, 41], [141, 42], [142, 43], [143, 44], [144, 45], [145, 46], [146, 46], [147, 47], [148, 48], [149, 49], [150, 50], [151, 51], [133, 2], [152, 52], [153, 53], [154, 54], [155, 55], [156, 56], [157, 57], [158, 58], [159, 59], [160, 60], [161, 61], [162, 62], [163, 63], [164, 64], [165, 65], [166, 66], [168, 67], [167, 68], [169, 69], [170, 70], [171, 2], [172, 71], [173, 72], [174, 73], [175, 74], [132, 75], [131, 2], [184, 76], [176, 77], [177, 78], [178, 79], [179, 80], [180, 81], [181, 82], [182, 83], [183, 84], [240, 2], [241, 2], [242, 2], [212, 2], [213, 2], [61, 85], [185, 85], [79, 85], [249, 86], [243, 87], [57, 2], [59, 88], [60, 85], [250, 34], [251, 2], [276, 89], [277, 90], [252, 91], [255, 91], [274, 89], [275, 89], [265, 89], [264, 92], [262, 89], [257, 89], [270, 89], [268, 89], [272, 89], [256, 89], [269, 89], [273, 89], [258, 89], [259, 89], [271, 89], [253, 89], [260, 89], [261, 89], [263, 89], [267, 89], [278, 93], [266, 89], [254, 89], [291, 94], [290, 2], [285, 93], [287, 95], [286, 93], [279, 93], [280, 93], [282, 93], [284, 93], [288, 95], [289, 95], [281, 95], [283, 95], [211, 96], [292, 97], [222, 98], [293, 21], [294, 2], [296, 99], [295, 2], [297, 2], [298, 100], [299, 2], [300, 101], [89, 2], [188, 2], [58, 2], [189, 2], [191, 102], [193, 103], [192, 102], [190, 8], [194, 104], [69, 105], [68, 2], [82, 106], [245, 107], [248, 108], [247, 2], [244, 85], [246, 2], [186, 109], [85, 110], [81, 2], [84, 2], [11, 2], [12, 2], [14, 2], [13, 2], [2, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [3, 2], [4, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [50, 2], [9, 2], [51, 2], [52, 2], [53, 2], [54, 2], [55, 2], [1, 2], [10, 2], [56, 2], [120, 111], [121, 111], [122, 111], [123, 111], [124, 111], [125, 112], [119, 2], [118, 113], [117, 114], [91, 115], [95, 115], [90, 116], [200, 115], [93, 115], [116, 117], [113, 118], [112, 119], [106, 120], [103, 121], [102, 122], [101, 120], [100, 123], [115, 124], [99, 125], [114, 120], [111, 122], [109, 126], [104, 127], [108, 128], [107, 129], [110, 130], [105, 128], [127, 131], [187, 132], [126, 133], [199, 4], [92, 134], [96, 139], [98, 136], [97, 137], [94, 139]], "semanticDiagnosticsPerFile": [203, 201, 87, 86, 83, 76, 73, 72, 67, 78, 63, 74, 66, 65, 75, 70, 77, 71, 64, 198, 197, 196, 80, 62, 206, 202, 204, 205, 208, 209, 215, 207, 220, 216, 219, 217, 214, 224, 223, 225, 226, 230, 231, 227, 228, 229, 232, 233, 221, 234, 235, 236, 237, 195, 218, 238, 210, 239, 134, 135, 136, 137, 138, 139, 130, 128, 129, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 133, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 168, 167, 169, 170, 171, 172, 173, 174, 175, 132, 131, 184, 176, 177, 178, 179, 180, 181, 182, 183, 240, 241, 242, 212, 213, 61, 185, 79, 249, 243, 57, 59, 60, 250, 251, 276, 277, 252, 255, 274, 275, 265, 264, 262, 257, 270, 268, 272, 256, 269, 273, 258, 259, 271, 253, 260, 261, 263, 267, 278, 266, 254, 291, 290, 285, 287, 286, 279, 280, 282, 284, 288, 289, 281, 283, 211, 292, 222, 293, 294, 296, 295, 297, 298, 299, 300, 89, 188, 58, 189, 191, 193, 192, 190, 194, 69, 68, 82, 245, 248, 247, 244, 246, 186, 85, 81, 84, 11, 12, 14, 13, 2, 15, 16, 17, 18, 19, 20, 21, 22, 3, 4, 26, 23, 24, 25, 27, 28, 29, 5, 30, 31, 32, 33, 6, 37, 34, 35, 36, 38, 7, 39, 44, 45, 40, 41, 42, 43, 8, 49, 46, 47, 48, 50, 9, 51, 52, 53, 54, 55, 1, 10, 56, 120, 121, 122, 123, 124, 125, 119, 118, 117, 91, 95, 90, 200, 93, [116, [{"file": "../../src/components/Auth/Login.tsx", "start": 794, "length": 15, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<LoginResponse, LoginRequest, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], [113, [{"file": "../../src/components/Categories/CategoryManagement.tsx", "start": 692, "length": 40, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<PaginatedResponse<CategoryWithStats> | ApiResponse<CategoryWithStats[]>, { page?: number | undefined; limit?: number | undefined; include_stats?: boolean | undefined; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Categories/CategoryManagement.tsx", "start": 1124, "length": 35, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"file": "../../src/components/Categories/CategoryManagement.tsx", "start": 1133, "length": 18, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<number, number, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], [112, [{"file": "../../src/components/Categories/CategoryModal.tsx", "start": 1480, "length": 68, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"file": "../../src/components/Categories/CategoryModal.tsx", "start": 1489, "length": 51, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<Category, { id: number; data: UpdateCategoryRequest; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Categories/CategoryModal.tsx", "start": 1581, "length": 41, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"file": "../../src/components/Categories/CategoryModal.tsx", "start": 1590, "length": 24, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<Category, CreateCategoryRequest, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], 106, [103, [{"file": "../../src/components/Dashboard/Dashboard.tsx", "start": 578, "length": 17, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<VideoStats, void, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Dashboard/Dashboard.tsx", "start": 611, "length": 25, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<PaginatedResponse<VideoWithDetails>, { page?: number | undefined; limit?: number | undefined; category_id?: number | undefined; status?: string | undefined; user_id?: number | undefined; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], 102, 101, 100, 115, [99, [{"file": "../../src/components/Layout/Sidebar.tsx", "start": 1099, "length": 8, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<void, void, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], 114, 111, [109, [{"file": "../../src/components/Videos/UploadVideoModal.tsx", "start": 2007, "length": 169, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"file": "../../src/components/Videos/UploadVideoModal.tsx", "start": 2016, "length": 152, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<VideoWithDetails, CreateVideoRequest, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Videos/UploadVideoModal.tsx", "start": 2822, "length": 58, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"file": "../../src/components/Videos/UploadVideoModal.tsx", "start": 2831, "length": 41, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<number, { id: number; file: File; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], [104, [{"file": "../../src/components/Videos/VideoCard.tsx", "start": 1760, "length": 21, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<number, number, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Videos/VideoCard.tsx", "start": 1863, "length": 58, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<VideoWithDetails, { id: number; data: UpdateVideoRequest; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}]], 108, 107, [110, [{"file": "../../src/components/Videos/VideoManagement.tsx", "start": 871, "length": 23, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<Category[], void, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Videos/VideoManagement.tsx", "start": 949, "length": 20, "code": 2345, "category": 1, "messageText": "Argument of type 'AsyncThunkAction<PaginatedResponse<VideoWithDetails>, { page?: number | undefined; limit?: number | undefined; category_id?: number | undefined; status?: string | undefined; user_id?: number | undefined; }, AsyncThunkConfig>' is not assignable to parameter of type 'UnknownAction'."}, {"file": "../../src/components/Videos/VideoManagement.tsx", "start": 2076, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ current_page: number; total_pages: number; total_items: number; limit: number; } | null' is not assignable to type '{ currentPage: number; totalPages: number; totalItems: number; limit: number; } | null'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ current_page: number; total_pages: number; total_items: number; limit: number; }' is missing the following properties from type '{ currentPage: number; totalPages: number; totalItems: number; limit: number; }': currentPage, totalPages, totalItems", "category": 1, "code": 2739}]}, "relatedInformation": [{"file": "../../src/components/Videos/VideoGrid.tsx", "start": 304, "length": 10, "messageText": "The expected type comes from property 'pagination' which is declared here on type 'IntrinsicAttributes & VideoGridProps'", "category": 3, "code": 6500}]}]], 105, 127, 187, 126, 199, 92, [96, [{"file": "../../src/store/categoriesSlice.ts", "start": 3635, "length": 10, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'categories' does not exist on type 'CategoryWithStats[] | { videos?: CategoryWithStats[] | undefined; categories?: CategoryWithStats[] | undefined; users?: CategoryWithStats[] | undefined; pagination?: { ...; } | undefined; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'categories' does not exist on type 'CategoryWithStats[]'.", "category": 1, "code": 2339}]}}, {"file": "../../src/store/categoriesSlice.ts", "start": 3717, "length": 10, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'pagination' does not exist on type 'CategoryWithStats[] | { videos?: CategoryWithStats[] | undefined; categories?: CategoryWithStats[] | undefined; users?: CategoryWithStats[] | undefined; pagination?: { ...; } | undefined; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'pagination' does not exist on type 'CategoryWithStats[]'.", "category": 1, "code": 2339}]}}]], 98, 97, [94, [{"file": "../../src/store/videosSlice.ts", "start": 4005, "length": 12, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'VideoWithDetails[] | { videos?: VideoWithDetails[] | undefined; categories?: VideoWithDetails[] | undefined; users?: VideoWithDetails[] | undefined; pagination?: { ...; } | undefined; }' is not assignable to type 'WritableDraft<VideoWithDetails>[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ videos?: VideoWithDetails[] | undefined; categories?: VideoWithDetails[] | undefined; users?: VideoWithDetails[] | undefined; pagination?: { ...; } | undefined; }' is missing the following properties from type 'WritableDraft<VideoWithDetails>[]': length, pop, push, concat, and 29 more.", "category": 1, "code": 2740}]}}]], 88], "affectedFilesPendingEmit": [[203, 1], [201, 1], [87, 1], [86, 1], [83, 1], [76, 1], [73, 1], [72, 1], [67, 1], [78, 1], [63, 1], [74, 1], [66, 1], [65, 1], [75, 1], [70, 1], [77, 1], [71, 1], [64, 1], [198, 1], [197, 1], [196, 1], [80, 1], [62, 1], [206, 1], [202, 1], [204, 1], [205, 1], [208, 1], [209, 1], [215, 1], [207, 1], [220, 1], [216, 1], [219, 1], [217, 1], [214, 1], [224, 1], [223, 1], [225, 1], [226, 1], [230, 1], [231, 1], [227, 1], [228, 1], [229, 1], [232, 1], [233, 1], [221, 1], [234, 1], [235, 1], [236, 1], [237, 1], [195, 1], [218, 1], [238, 1], [210, 1], [239, 1], [134, 1], [135, 1], [136, 1], [137, 1], [138, 1], [139, 1], [130, 1], [128, 1], [129, 1], [140, 1], [141, 1], [142, 1], [143, 1], [144, 1], [145, 1], [146, 1], [147, 1], [148, 1], [149, 1], [150, 1], [151, 1], [133, 1], [152, 1], [153, 1], [154, 1], [155, 1], [156, 1], [157, 1], [158, 1], [159, 1], [160, 1], [161, 1], [162, 1], [163, 1], [164, 1], [165, 1], [166, 1], [168, 1], [167, 1], [169, 1], [170, 1], [171, 1], [172, 1], [173, 1], [174, 1], [175, 1], [132, 1], [131, 1], [184, 1], [176, 1], [177, 1], [178, 1], [179, 1], [180, 1], [181, 1], [182, 1], [183, 1], [240, 1], [241, 1], [242, 1], [212, 1], [213, 1], [61, 1], [185, 1], [79, 1], [249, 1], [243, 1], [57, 1], [59, 1], [60, 1], [250, 1], [251, 1], [276, 1], [277, 1], [252, 1], [255, 1], [274, 1], [275, 1], [265, 1], [264, 1], [262, 1], [257, 1], [270, 1], [268, 1], [272, 1], [256, 1], [269, 1], [273, 1], [258, 1], [259, 1], [271, 1], [253, 1], [260, 1], [261, 1], [263, 1], [267, 1], [278, 1], [266, 1], [254, 1], [291, 1], [290, 1], [285, 1], [287, 1], [286, 1], [279, 1], [280, 1], [282, 1], [284, 1], [288, 1], [289, 1], [281, 1], [283, 1], [211, 1], [292, 1], [222, 1], [293, 1], [294, 1], [296, 1], [295, 1], [297, 1], [298, 1], [299, 1], [300, 1], [89, 1], [188, 1], [58, 1], [189, 1], [191, 1], [193, 1], [192, 1], [190, 1], [194, 1], [69, 1], [68, 1], [82, 1], [245, 1], [248, 1], [247, 1], [244, 1], [246, 1], [186, 1], [85, 1], [81, 1], [84, 1], [2, 1], [3, 1], [4, 1], [5, 1], [6, 1], [7, 1], [8, 1], [9, 1], [10, 1], [120, 1], [121, 1], [122, 1], [123, 1], [124, 1], [125, 1], [119, 1], [118, 1], [117, 1], [91, 1], [95, 1], [90, 1], [200, 1], [93, 1], [116, 1], [113, 1], [112, 1], [106, 1], [103, 1], [102, 1], [101, 1], [100, 1], [115, 1], [99, 1], [114, 1], [111, 1], [109, 1], [104, 1], [108, 1], [107, 1], [110, 1], [105, 1], [127, 1], [187, 1], [126, 1], [199, 1], [92, 1], [96, 1], [98, 1], [97, 1], [94, 1], [88, 1]]}, "version": "4.9.5"}