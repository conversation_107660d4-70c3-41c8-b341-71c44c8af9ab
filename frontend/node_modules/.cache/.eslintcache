[{"/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Layout.tsx": "4", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Auth/Login.tsx": "5", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/index.ts": "6", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/authSlice.ts": "7", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Header.tsx": "8", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Sidebar.tsx": "9", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Users/<USER>": "10", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Settings/Settings.tsx": "11", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/Dashboard.tsx": "12", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/uiSlice.ts": "13", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/videosSlice.ts": "14", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoManagement.tsx": "15", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Categories/CategoryManagement.tsx": "16", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/categoriesSlice.ts": "17", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/auth.ts": "18", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/videos.ts": "19", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/categories.ts": "20", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/RecentVideos.tsx": "21", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/StatsCard.tsx": "22", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoFilters.tsx": "23", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoGrid.tsx": "24", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/UploadVideoModal.tsx": "25", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Categories/CategoryModal.tsx": "26", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/client.ts": "27", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoCard.tsx": "28", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoPlayerModal.tsx": "29", "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Common/Pagination.tsx": "30"}, {"size": 554, "mtime": 1753953823518, "results": "31", "hashOfConfig": "32"}, {"size": 425, "mtime": 1753953823518, "results": "33", "hashOfConfig": "32"}, {"size": 610, "mtime": 1753954689501, "results": "34", "hashOfConfig": "32"}, {"size": 1495, "mtime": 1753954220675, "results": "35", "hashOfConfig": "32"}, {"size": 5011, "mtime": 1753954658145, "results": "36", "hashOfConfig": "32"}, {"size": 727, "mtime": 1753956166426, "results": "37", "hashOfConfig": "32"}, {"size": 2804, "mtime": 1753954068323, "results": "38", "hashOfConfig": "32"}, {"size": 1435, "mtime": 1753954207380, "results": "39", "hashOfConfig": "32"}, {"size": 3741, "mtime": 1753954189804, "results": "40", "hashOfConfig": "32"}, {"size": 6057, "mtime": 1753954585560, "results": "41", "hashOfConfig": "32"}, {"size": 7367, "mtime": 1753954631265, "results": "42", "hashOfConfig": "32"}, {"size": 2485, "mtime": 1753954239774, "results": "43", "hashOfConfig": "32"}, {"size": 1065, "mtime": 1753954154885, "results": "44", "hashOfConfig": "32"}, {"size": 5806, "mtime": 1753956189487, "results": "45", "hashOfConfig": "32"}, {"size": 2430, "mtime": 1753954294377, "results": "46", "hashOfConfig": "32"}, {"size": 5029, "mtime": 1753954500108, "results": "47", "hashOfConfig": "32"}, {"size": 6067, "mtime": 1753956200638, "results": "48", "hashOfConfig": "32"}, {"size": 675, "mtime": 1753953987207, "results": "49", "hashOfConfig": "32"}, {"size": 2046, "mtime": 1753953999366, "results": "50", "hashOfConfig": "32"}, {"size": 2034, "mtime": 1753954031880, "results": "51", "hashOfConfig": "32"}, {"size": 3530, "mtime": 1753954275203, "results": "52", "hashOfConfig": "32"}, {"size": 880, "mtime": 1753954255944, "results": "53", "hashOfConfig": "32"}, {"size": 2540, "mtime": 1753954308713, "results": "54", "hashOfConfig": "32"}, {"size": 2582, "mtime": 1753954337109, "results": "55", "hashOfConfig": "32"}, {"size": 9442, "mtime": 1753954444243, "results": "56", "hashOfConfig": "32"}, {"size": 7924, "mtime": 1753954552737, "results": "57", "hashOfConfig": "32"}, {"size": 968, "mtime": 1753953979953, "results": "58", "hashOfConfig": "32"}, {"size": 6115, "mtime": 1753954370935, "results": "59", "hashOfConfig": "32"}, {"size": 3351, "mtime": 1753954397824, "results": "60", "hashOfConfig": "32"}, {"size": 3116, "mtime": 1753954464176, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1l63rc7", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Auth/Login.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/index.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/authSlice.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Header.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Layout/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Users/<USER>", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Settings/Settings.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/uiSlice.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/videosSlice.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoManagement.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Categories/CategoryManagement.tsx", ["152", "153"], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/store/categoriesSlice.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/auth.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/videos.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/categories.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/RecentVideos.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Dashboard/StatsCard.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoFilters.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoGrid.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/UploadVideoModal.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Categories/CategoryModal.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/api/client.ts", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoCard.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Videos/VideoPlayerModal.tsx", [], [], "/Users/<USER>/Desktop/Projects/CoreMovie/frontend/src/components/Common/Pagination.tsx", [], [], {"ruleId": "154", "severity": 1, "message": "155", "line": 4, "column": 27, "nodeType": "156", "messageId": "157", "endLine": 4, "endColumn": 41}, {"ruleId": "154", "severity": 1, "message": "158", "line": 4, "column": 43, "nodeType": "156", "messageId": "157", "endLine": 4, "endColumn": 57}, "@typescript-eslint/no-unused-vars", "'createCategory' is defined but never used.", "Identifier", "unusedVar", "'updateCategory' is defined but never used."]