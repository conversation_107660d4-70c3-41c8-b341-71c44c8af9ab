{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AAoBM,SAAS,0CAAoB,KAA4B,EAAE,GAAkC;IAClG,IAAI,cAAC,UAAU,cAAE,UAAU,gBAAE,eAAe,GAAE,GAAG;IAEjD,IAAI,mBAAmB,CAAA,GAAA,aAAK,EAAwB;IAEpD,IAAI,kBAAkB,CAAA,GAAA,yCAAa,EAAE,CAAC;QACpC,4FAA4F;QAC5F,iFAAiF;QACjF,KAAK,IAAI,SAAS,QAChB,yHAAyH;QACzH,4EAA4E;QAC5E,IAAI,MAAM,cAAc,IAAI,YAC1B;IAGN;IAEA,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,IAAI,OAAO,EAAE;YACf,mKAAmK;YACnK,0MAA0M;YAC1M,yFAAyF;YACzF,iBAAiB,OAAO,GAAG,IAAI,qBAAqB,iBAAiB;gBAAC,MAAM,CAAA,GAAA,yCAAc,EAAE,gBAAA,0BAAA,IAAK,OAAO;gBAAkB,YAAY,CAAC,IAAI,EAAE,MAAM,aAAa,EAAE,EAAE,MAAM,aAAa,EAAE,EAAE,MAAM,aAAa,CAAC,CAAC;YAAA;YAChN,iBAAiB,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO;QAC9C;QAEA,OAAO;YACL,IAAI,iBAAiB,OAAO,EAC1B,iBAAiB,OAAO,CAAC,UAAU;QAEvC;IACF,GAAG;QAAC;QAAY;QAAiB;QAAK;KAAa;AACrD", "sources": ["packages/@react-aria/utils/src/useLoadMoreSentinel.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {AsyncLoadable, Collection} from '@react-types/shared';\nimport {getScrollParent} from './getScrollParent';\nimport {RefObject, useRef} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport interface LoadMoreSentinelProps extends Omit<AsyncLoadable, 'isLoading'> {\n  collection: Collection<any>,\n  /**\n   * The amount of offset from the bottom of your scrollable region that should trigger load more.\n   * Uses a percentage value relative to the scroll body's client height. Load more is then triggered\n   * when your current scroll position's distance from the bottom of the currently loaded list of items is less than\n   * or equal to the provided value. (e.g. 1 = 100% of the scroll region's height).\n   * @default 1\n   */\n  scrollOffset?: number\n}\n\nexport function useLoadMoreSentinel(props: LoadMoreSentinelProps, ref: RefObject<HTMLElement | null>): void {\n  let {collection, onLoadMore, scrollOffset = 1} = props;\n\n  let sentinelObserver = useRef<IntersectionObserver>(null);\n\n  let triggerLoadMore = useEffectEvent((entries: IntersectionObserverEntry[]) => {\n    // Use \"isIntersecting\" over an equality check of 0 since it seems like there is cases where\n    // a intersection ratio of 0 can be reported when isIntersecting is actually true\n    for (let entry of entries) {\n      // Note that this will be called if the collection changes, even if onLoadMore was already called and is being processed.\n      // Up to user discretion as to how to handle these multiple onLoadMore calls\n      if (entry.isIntersecting && onLoadMore) {\n        onLoadMore();\n      }\n    }\n  });\n\n  useLayoutEffect(() => {\n    if (ref.current) {\n      // Tear down and set up a new IntersectionObserver when the collection changes so that we can properly trigger additional loadMores if there is room for more items\n      // Need to do this tear down and set up since using a large rootMargin will mean the observer's callback isn't called even when scrolling the item into view beause its visibility hasn't actually changed\n      // https://codesandbox.io/p/sandbox/magical-swanson-dhgp89?file=%2Fsrc%2FApp.js%3A21%2C21\n      sentinelObserver.current = new IntersectionObserver(triggerLoadMore, {root: getScrollParent(ref?.current) as HTMLElement, rootMargin: `0px ${100 * scrollOffset}% ${100 * scrollOffset}% ${100 * scrollOffset}%`});\n      sentinelObserver.current.observe(ref.current);\n    }\n\n    return () => {\n      if (sentinelObserver.current) {\n        sentinelObserver.current.disconnect();\n      }\n    };\n  }, [collection, triggerLoadMore, ref, scrollOffset]);\n}\n"], "names": [], "version": 3, "file": "useLoadMoreSentinel.module.js.map"}