{"mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,SAAS,oCAAc,EAAU;QAIlB;IAHb,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,MACvD,OAAO;IAET,IAAI,UAAS,kCAAA,OAAO,SAAS,CAAC,gBAAgB,cAAjC,sDAAA,gCAAmC,MAAM;IACtD,OAAO,MAAM,OAAO,CAAC,WAAW,OAAO,IAAI,CAAC,CAAC,QAA4C,GAAG,IAAI,CAAC,MAAM,KAAK,MAC1G,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AACtC;AAEA,SAAS,mCAAa,EAAU;QAElB;IADZ,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OACxD,GAAG,IAAI,CAAC,EAAA,kCAAA,OAAO,SAAS,CAAC,gBAAgB,cAAjC,sDAAA,gCAAmC,QAAQ,KAAI,OAAO,SAAS,CAAC,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,EAAiB;IAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAC3B,OAAO;IAGT,IAAI,MAAsB;IAC1B,OAAO;QACL,IAAI,OAAO,MACT,MAAM;QAER,OAAO;IACT;AACF;AAEO,MAAM,4CAAuB,6BAAO;IACzC,OAAO,mCAAa;AACtB;AAEO,MAAM,2CAA0B,6BAAO;IAC5C,OAAO,mCAAa;AACtB;AAEO,MAAM,4CAAwB,6BAAO;IAC1C,OAAO,mCAAa,aAClB,yFAAyF;IACxF,+CAAW,UAAU,cAAc,GAAG;AAC3C;AAEO,MAAM,4CAAuB,6BAAO;IACzC,OAAO,8CAAc;AACvB;AAEO,MAAM,4CAA+B,6BAAO;IACjD,OAAO,+CAAW;AACpB;AAEO,MAAM,4CAA0B,6BAAO;IAC5C,OAAO,oCAAc,mBAAmB,CAAC;AAC3C;AAEO,MAAM,4CAA0B,6BAAO;IAC5C,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAA2B,6BAAO;IAC7C,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAA2B,6BAAO;IAC7C,OAAO,oCAAc;AACvB", "sources": ["packages/@react-aria/utils/src/platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  let brands = window.navigator['userAgentData']?.brands;\n  return Array.isArray(brands) && brands.some((brand: {brand: string, version: string}) => re.test(brand.brand)) ||\n    re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n\n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac: () => boolean = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone: () => boolean = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad: () => boolean = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS: () => boolean = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice: () => boolean = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit: () => boolean = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome: () => boolean = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid: () => boolean = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox: () => boolean = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "names": [], "version": 3, "file": "platform.main.js.map"}