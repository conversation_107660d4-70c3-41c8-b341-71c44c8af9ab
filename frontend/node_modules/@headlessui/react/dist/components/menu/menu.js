"use client";import{useFocusRing as Te}from"@react-aria/focus";import{useHover as fe}from"@react-aria/interactions";import L,{Fragment as oe,useCallback as K,useEffect as ye,useMemo as J,useRef as V,useState as Pe}from"react";import{flushSync as q}from"react-dom";import{useActivePress as Ee}from'../../hooks/use-active-press.js';import{useDidElementMove as ge}from'../../hooks/use-did-element-move.js';import{useDisposables as Me}from'../../hooks/use-disposables.js';import{useElementSize as be}from'../../hooks/use-element-size.js';import{useEvent as d}from'../../hooks/use-event.js';import{useId as W}from'../../hooks/use-id.js';import{useInertOthers as Ae}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as X}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as _e}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ie}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ne}from'../../hooks/use-owner.js';import{Action as Q,useQuickRelease as Re}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Se}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as De}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as $}from'../../hooks/use-sync-refs.js';import{useTextValue as Fe}from'../../hooks/use-text-value.js';import{useTrackedPointer as he}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as xe,useTransition as Ce}from'../../hooks/use-transition.js';import{useTreeWalker as Le}from'../../hooks/use-tree-walker.js';import{FloatingProvider as ve,useFloatingPanel as Oe,useFloatingPanelProps as He,useFloatingReference as Ue,useFloatingReferenceProps as Ge,useResolvedAnchor as ke}from'../../internal/floating.js';import{OpenClosedProvider as Ne,State as j,useOpenClosed as Be}from'../../internal/open-closed.js';import{stackMachines as we}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{isDisabledReactIssue7711 as Ke}from'../../utils/bugs.js';import{Focus as I}from'../../utils/calculate-active-index.js';import{disposables as We}from'../../utils/disposables.js';import*as Je from'../../utils/dom.js';import{Focus as re,FocusableMode as Ve,focusFrom as Xe,isFocusableElement as Qe,restoreFocusIfNecessary as ae}from'../../utils/focus-management.js';import{match as $e}from'../../utils/match.js';import{RenderFeatures as se,forwardRefWithAs as v,mergeProps as le,useRender as O}from'../../utils/render.js';import{useDescriptions as je}from'../description/description.js';import{Keys as c}from'../keyboard.js';import{useLabelContext as qe,useLabels as pe}from'../label/label.js';import{MouseButton as ze}from'../mouse.js';import{Portal as Ye}from'../portal/portal.js';import{ActionTypes as r,ActivationTrigger as z,MenuState as T}from'./menu-machine.js';import{MenuContext as Ze,useMenuMachine as et,useMenuMachineContext as Y}from'./menu-machine-glue.js';let tt=oe;function ot(f,g){let p=W(),{__demoMode:s=!1,...l}=f,a=et({id:p,__demoMode:s}),[n,A,E]=D(a,y=>[y.menuState,y.itemsElement,y.buttonElement]),R=$(g),o=we.get(null),F=D(o,K(y=>o.selectors.isTop(y,p),[o,p]));Ie(F,[E,A],(y,u)=>{var P;a.send({type:r.CloseMenu}),Qe(u,Ve.Loose)||(y.preventDefault(),(P=a.state.buttonElement)==null||P.focus())});let _=d(()=>{a.send({type:r.CloseMenu})}),M=J(()=>({open:n===T.Open,close:_}),[n,_]),m={ref:R},b=O();return L.createElement(ve,null,L.createElement(Ze.Provider,{value:a},L.createElement(Ne,{value:$e(n,{[T.Open]:j.Open,[T.Closed]:j.Closed})},b({ourProps:m,theirProps:l,slot:M,defaultTag:tt,name:"Menu"}))))}let nt="button";function rt(f,g){let p=Y("Menu.Button"),s=W(),{id:l=`headlessui-menu-button-${s}`,disabled:a=!1,autoFocus:n=!1,...A}=f,E=V(null),R=Ge(),o=$(g,E,Ue(),d(t=>p.send({type:r.SetButtonElement,element:t}))),F=d(t=>{switch(t.key){case c.Space:case c.Enter:case c.ArrowDown:t.preventDefault(),t.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:I.First}});break;case c.ArrowUp:t.preventDefault(),t.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:I.Last}});break}}),_=d(t=>{switch(t.key){case c.Space:t.preventDefault();break}}),[M,m,b]=D(p,t=>[t.menuState,t.buttonElement,t.itemsElement]),y=M===T.Open;Re(y,{trigger:m,action:K(t=>{if(m!=null&&m.contains(t.target))return Q.Ignore;let C=t.target.closest('[role="menuitem"]:not([data-disabled])');return Je.isHTMLElement(C)?Q.Select(C):b!=null&&b.contains(t.target)?Q.Ignore:Q.Close},[m,b]),close:K(()=>p.send({type:r.CloseMenu}),[]),select:K(t=>t.click(),[])});let u=d(t=>{var C;if(t.button===ze.Left){if(Ke(t.currentTarget))return t.preventDefault();a||(M===T.Open?(q(()=>p.send({type:r.CloseMenu})),(C=E.current)==null||C.focus({preventScroll:!0})):(t.preventDefault(),p.send({type:r.OpenMenu,focus:{focus:I.Nothing},trigger:z.Pointer})))}}),P=V(null),H=d(t=>{P.current=t.pointerType,t.pointerType==="mouse"&&u(t)}),S=d(t=>{P.current!=="mouse"&&u(t)}),{isFocusVisible:h,focusProps:x}=Te({autoFocus:n}),{isHovered:U,hoverProps:G}=fe({isDisabled:a}),{pressed:k,pressProps:N}=Ee({disabled:a}),i=J(()=>({open:M===T.Open,active:k||M===T.Open,disabled:a,hover:U,focus:h,autofocus:n}),[M,U,h,k,a,n]),B=le(R(),{ref:o,id:l,type:Se(f,E.current),"aria-haspopup":"menu","aria-controls":b==null?void 0:b.id,"aria-expanded":M===T.Open,disabled:a||void 0,autoFocus:n,onKeyDown:F,onKeyUp:_,onPointerDown:H,onClick:S},x,G,N);return O()({ourProps:B,theirProps:A,slot:i,defaultTag:nt,name:"Menu.Button"})}let at="div",st=se.RenderStrategy|se.Static;function lt(f,g){let p=W(),{id:s=`headlessui-menu-items-${p}`,anchor:l,portal:a=!1,modal:n=!0,transition:A=!1,...E}=f,R=ke(l),o=Y("Menu.Items"),[F,_]=Oe(R),M=He(),[m,b]=Pe(null),y=$(g,R?F:null,d(e=>o.send({type:r.SetItemsElement,element:e})),b),[u,P]=D(o,e=>[e.menuState,e.buttonElement]),H=ne(P),S=ne(m);R&&(a=!0);let h=Be(),[x,U]=Ce(A,m,h!==null?(h&j.Open)===j.Open:u===T.Open);_e(x,P,()=>{o.send({type:r.CloseMenu})});let G=D(o,e=>e.__demoMode),k=G?!1:n&&u===T.Open;De(k,S);let N=G?!1:n&&u===T.Open;Ae(N,{allowed:K(()=>[P,m],[P,m])});let i=u!==T.Open,Z=ge(i,P)?!1:x;ye(()=>{let e=m;e&&u===T.Open&&e!==(S==null?void 0:S.activeElement)&&e.focus({preventScroll:!0})},[u,m,S]),Le(u===T.Open,{container:m,accept(e){return e.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});let t=Me(),C=d(e=>{var w,ee,te;switch(t.dispose(),e.key){case c.Space:if(o.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),o.send({type:r.Search,value:e.key});case c.Enter:if(e.preventDefault(),e.stopPropagation(),o.state.activeItemIndex!==null){let{dataRef:ce}=o.state.items[o.state.activeItemIndex];(ee=(w=ce.current)==null?void 0:w.domRef.current)==null||ee.click()}o.send({type:r.CloseMenu}),ae(o.state.buttonElement);break;case c.ArrowDown:return e.preventDefault(),e.stopPropagation(),o.send({type:r.GoToItem,focus:I.Next});case c.ArrowUp:return e.preventDefault(),e.stopPropagation(),o.send({type:r.GoToItem,focus:I.Previous});case c.Home:case c.PageUp:return e.preventDefault(),e.stopPropagation(),o.send({type:r.GoToItem,focus:I.First});case c.End:case c.PageDown:return e.preventDefault(),e.stopPropagation(),o.send({type:r.GoToItem,focus:I.Last});case c.Escape:e.preventDefault(),e.stopPropagation(),q(()=>o.send({type:r.CloseMenu})),(te=o.state.buttonElement)==null||te.focus({preventScroll:!0});break;case c.Tab:e.preventDefault(),e.stopPropagation(),q(()=>o.send({type:r.CloseMenu})),Xe(o.state.buttonElement,e.shiftKey?re.Previous:re.Next);break;default:e.key.length===1&&(o.send({type:r.Search,value:e.key}),t.setTimeout(()=>o.send({type:r.ClearSearch}),350));break}}),ie=d(e=>{switch(e.key){case c.Space:e.preventDefault();break}}),ue=J(()=>({open:u===T.Open}),[u]),me=le(R?M():{},{"aria-activedescendant":D(o,o.selectors.activeDescendantId),"aria-labelledby":D(o,e=>{var w;return(w=e.buttonElement)==null?void 0:w.id}),id:s,onKeyDown:C,onKeyUp:ie,role:"menu",tabIndex:u===T.Open?0:void 0,ref:y,style:{...E.style,..._,"--button-width":be(P,!0).width},...xe(U)}),de=O();return L.createElement(Ye,{enabled:a?f.static||x:!1,ownerDocument:H},de({ourProps:me,theirProps:E,slot:ue,defaultTag:at,features:st,visible:Z,name:"Menu.Items"}))}let pt=oe;function it(f,g){let p=W(),{id:s=`headlessui-menu-item-${p}`,disabled:l=!1,...a}=f,n=Y("Menu.Item"),A=D(n,i=>n.selectors.isActive(i,s)),E=V(null),R=$(g,E),o=D(n,i=>n.selectors.shouldScrollIntoView(i,s));X(()=>{if(o)return We().requestAnimationFrame(()=>{var i,B;(B=(i=E.current)==null?void 0:i.scrollIntoView)==null||B.call(i,{block:"nearest"})})},[o,E]);let F=Fe(E),_=V({disabled:l,domRef:E,get textValue(){return F()}});X(()=>{_.current.disabled=l},[_,l]),X(()=>(n.actions.registerItem(s,_),()=>n.actions.unregisterItem(s)),[_,s]);let M=d(()=>{n.send({type:r.CloseMenu})}),m=d(i=>{if(l)return i.preventDefault();n.send({type:r.CloseMenu}),ae(n.state.buttonElement)}),b=d(()=>{if(l)return n.send({type:r.GoToItem,focus:I.Nothing});n.send({type:r.GoToItem,focus:I.Specific,id:s})}),y=he(),u=d(i=>y.update(i)),P=d(i=>{y.wasMoved(i)&&(l||A||n.send({type:r.GoToItem,focus:I.Specific,id:s,trigger:z.Pointer}))}),H=d(i=>{y.wasMoved(i)&&(l||A&&n.state.activationTrigger===z.Pointer&&n.send({type:r.GoToItem,focus:I.Nothing}))}),[S,h]=pe(),[x,U]=je(),G=J(()=>({active:A,focus:A,disabled:l,close:M}),[A,l,M]),k={id:s,ref:R,role:"menuitem",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-labelledby":S,"aria-describedby":x,disabled:void 0,onClick:m,onFocus:b,onPointerEnter:u,onMouseEnter:u,onPointerMove:P,onMouseMove:P,onPointerLeave:H,onMouseLeave:H},N=O();return L.createElement(h,null,L.createElement(U,null,N({ourProps:k,theirProps:a,slot:G,defaultTag:pt,name:"Menu.Item"})))}let ut="div";function mt(f,g){let[p,s]=pe(),l=f,a={ref:g,"aria-labelledby":p,role:"group"},n=O();return L.createElement(s,null,n({ourProps:a,theirProps:l,slot:{},defaultTag:ut,name:"Menu.Section"}))}let dt="header";function ct(f,g){let p=W(),{id:s=`headlessui-menu-heading-${p}`,...l}=f,a=qe();X(()=>a.register(s),[s,a.register]);let n={id:s,ref:g,role:"presentation",...a.props};return O()({ourProps:n,theirProps:l,slot:{},defaultTag:dt,name:"Menu.Heading"})}let Tt="div";function ft(f,g){let p=f,s={ref:g,role:"separator"};return O()({ourProps:s,theirProps:p,slot:{},defaultTag:Tt,name:"Menu.Separator"})}let yt=v(ot),Pt=v(rt),Et=v(lt),gt=v(it),Mt=v(mt),bt=v(ct),At=v(ft),io=Object.assign(yt,{Button:Pt,Items:Et,Item:gt,Section:Mt,Heading:bt,Separator:At});export{io as Menu,Pt as MenuButton,bt as MenuHeading,gt as MenuItem,Et as MenuItems,Mt as MenuSection,At as MenuSeparator};
