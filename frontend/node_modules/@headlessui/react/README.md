<h3 align="center">
  @headlessui/react
</h3>

<p align="center">
  A set of completely unstyled, fully accessible UI components for React, designed to integrate
  beautifully with Tailwind CSS.
</p>

<p align="center">
  <a href="https://www.npmjs.com/package/@headlessui/react"><img src="https://img.shields.io/npm/dt/@headlessui/react.svg" alt="Total Downloads"></a>
  <a href="https://github.com/tailwindlabs/headlessui/releases"><img src="https://img.shields.io/npm/v/@headlessui/react.svg" alt="Latest Release"></a>
  <a href="https://github.com/tailwindlabs/headlessui/blob/main/LICENSE"><img src="https://img.shields.io/npm/l/@headlessui/react.svg" alt="License"></a>
</p>

## Installation

```sh
npm install @headlessui/react
```

## Documentation

For full documentation, visit [headlessui.dev](https://headlessui.dev/react/menu).

## Community

For help, discussion about best practices, or feature ideas:

[Discuss Headless UI on GitHub](https://github.com/tailwindlabs/headlessui/discussions)
