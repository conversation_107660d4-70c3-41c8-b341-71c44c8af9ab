{"name": "@react-stately/utils", "version": "3.10.8", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "dependencies": {"@swc/helpers": "^0.5.0"}, "publishConfig": {"access": "public"}, "gitHead": "8b9348ff255e018b2dd9b27e2a45507cadfa1d35"}