# CoreMovie
## 一个管理媒体文件的系统

### 用户管理
  1. 用户管理
  2. 添加用户
  3. 角色管理

### 媒体管理
  1. 视频上传
  2. 视频管理
  3. 分类管理

### 技术栈
1. GO
2. React + Redux
3. MySQL + xorm

### 项目结构

```
CoreMovie/
├── database/           # 数据库相关代码
├── models/             # 数据模型定义
├── routes/             # HTTP路由处理
├── frontend/           # 前端React应用
│   ├── src/
│   │   ├── components/ # React组件
│   │   ├── redux/      # Redux状态管理
│   │   ├── App.js      # 主应用组件
│   │   └── index.js    # 应用入口
├── main.go             # Go应用入口
└── go.mod              # Go模块定义
```

### 后端设置

1. 安装 Go 1.19 或更高版本
2. 设置 MySQL 数据库
3. 更新数据库连接信息在 [main.go](file:///Users/<USER>/Desktop/Projects/CoreMovie/main.go#L15-L15)
4. 运行以下命令启动后端服务：

```bash
go mod tidy
go run main.go
```

### 前端设置

1. 安装 Node.js 和 npm
2. 导航到前端目录并安装依赖：

```bash
cd frontend
npm install
```

3. 启动开发服务器：

```bash
npm start
```

这将在开发模式下启动应用，默认在 http://localhost:3000 上访问。