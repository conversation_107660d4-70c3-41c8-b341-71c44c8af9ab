<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vortex | Video Management Dashboard</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* A light gray background */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* Style for the active nav link */
        .nav-link.active {
            background-color: #4f46e5; /* Indigo */
            color: white;
        }
        /* Simple transition for interactive elements */
        .transition-all {
            transition: all 0.3s ease-in-out;
        }
        /* Video card play icon overlay */
        .video-thumbnail-container:hover .play-icon-overlay {
            opacity: 1;
        }
        /* Modal styles */
        .modal-overlay {
            transition: opacity 0.3s ease;
        }
        .modal-container {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-200">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-30 w-64 px-4 py-7 overflow-y-auto text-white transition-transform duration-300 ease-in-out transform -translate-x-full bg-gray-800 md:relative md:translate-x-0">
            <!-- Logo -->
            <div class="flex items-center justify-between px-2">
                <a href="#" class="flex items-center space-x-2 text-2xl font-bold">
                    <i class="fas fa-play-circle text-indigo-400"></i>
                    <span>Vortex</span>
                </a>
                <!-- Close button for mobile -->
                 <button id="close-sidebar" class="text-gray-400 md:hidden hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Navigation Links -->
            <nav class="mt-10">
                <a href="#dashboard" class="nav-link active flex items-center px-4 py-3 mt-4 text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white" onclick="showSection('dashboard', this)">
                    <i class="fas fa-tachometer-alt w-5 h-5"></i>
                    <span class="mx-4 font-medium">Dashboard</span>
                </a>
                <a href="#videos" class="nav-link flex items-center px-4 py-3 mt-4 text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white" onclick="showSection('videos', this)">
                    <i class="fas fa-video w-5 h-5"></i>
                    <span class="mx-4 font-medium">Videos</span>
                </a>
                <a href="#users" class="nav-link flex items-center px-4 py-3 mt-4 text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white" onclick="showSection('users', this)">
                    <i class="fas fa-users w-5 h-5"></i>
                    <span class="mx-4 font-medium">Users</span>
                </a>
                <a href="#categories" class="nav-link flex items-center px-4 py-3 mt-4 text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white" onclick="showSection('categories', this)">
                    <i class="fas fa-tags w-5 h-5"></i>
                    <span class="mx-4 font-medium">Categories</span>
                </a>
                 <a href="#settings" class="nav-link flex items-center px-4 py-3 mt-4 text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white" onclick="showSection('settings', this)">
                    <i class="fas fa-cog w-5 h-5"></i>
                    <span class="mx-4 font-medium">Settings</span>
                </a>
            </nav>
            
            <!-- User Profile -->
            <div class="absolute bottom-0 left-0 w-full p-4 border-t border-gray-700">
                <div class="flex items-center">
                    <img class="w-10 h-10 rounded-full" src="https://placehold.co/100x100/7e22ce/ffffff?text=A" alt="Admin Avatar">
                    <div class="ml-3">
                        <p class="text-sm font-medium text-white">Admin User</p>
                        <a href="#" class="text-xs text-indigo-400 hover:underline">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col flex-1 overflow-y-auto">
            <div class="flex items-center justify-between h-16 bg-white border-b border-gray-200 px-4 md:px-8">
                <!-- Mobile Menu Button -->
                <button id="open-sidebar" class="text-gray-500 md:hidden focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <!-- Search Bar -->
                <div class="relative w-full max-w-xs ml-auto">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <i class="fas fa-search text-gray-400"></i>
                    </span>
                    <input type="text" class="w-full py-2 pl-10 pr-4 text-gray-700 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="Search...">
                </div>
            </div>

            <div class="p-4 md:p-8">
                <!-- Dashboard Section -->
                <div id="dashboard" class="content-section">
                    <h1 class="text-3xl font-bold text-gray-800">Dashboard</h1>
                    <div class="grid grid-cols-1 gap-6 mt-6 sm:grid-cols-2 lg:grid-cols-4">
                        <div class="p-6 bg-white rounded-lg shadow-md">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 uppercase">Total Videos</p>
                                    <p class="text-3xl font-bold text-gray-800">1,250</p>
                                </div>
                                <div class="p-3 text-white bg-indigo-500 rounded-full">
                                    <i class="fas fa-video fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="p-6 bg-white rounded-lg shadow-md">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 uppercase">Total Users</p>
                                    <p class="text-3xl font-bold text-gray-800">84</p>
                                </div>
                                <div class="p-3 text-white bg-green-500 rounded-full">
                                    <i class="fas fa-users fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="p-6 bg-white rounded-lg shadow-md">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 uppercase">Storage Used</p>
                                    <p class="text-3xl font-bold text-gray-800">452 GB</p>
                                </div>
                                <div class="p-3 text-white bg-yellow-500 rounded-full">
                                    <i class="fas fa-database fa-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="p-6 bg-white rounded-lg shadow-md">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 uppercase">Total Views</p>
                                    <p class="text-3xl font-bold text-gray-800">2.1M</p>
                                </div>
                                <div class="p-3 text-white bg-red-500 rounded-full">
                                    <i class="fas fa-chart-bar fa-lg"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Recent Videos Table -->
                     <div class="mt-8 bg-white rounded-lg shadow-md">
                        <div class="p-4 border-b">
                            <h2 class="text-xl font-semibold text-gray-800">Recently Added Videos</h2>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <tbody class="bg-white">
                                    <!-- Table rows would be dynamically generated -->
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img class="w-20 h-12 object-cover rounded-md" src="https://placehold.co/160x90/a78bfa/ffffff?text=Tech" alt="Video thumbnail">
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Getting Started with React Hooks</div>
                                                    <div class="text-sm text-gray-500">Technology</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">July 31, 2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Published</span></td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img class="w-20 h-12 object-cover rounded-md" src="https://placehold.co/160x90/f87171/ffffff?text=Cooking" alt="Video thumbnail">
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Perfect Pasta Carbonara</div>
                                                    <div class="text-sm text-gray-500">Cooking</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">July 30, 2025</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Processing</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Video Management Section -->
                <div id="videos" class="hidden content-section">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <h1 class="text-3xl font-bold text-gray-800">Video Management</h1>
                        <button class="flex items-center px-4 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                            <i class="fas fa-upload mr-2"></i>
                            Upload Video
                        </button>
                    </div>
                    <!-- Filters -->
                    <div class="mt-6 mb-4">
                        <div class="flex space-x-2">
                            <button onclick="filterVideos('all')" class="category-filter active px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">All</button>
                            <button onclick="filterVideos('technology')" class="category-filter px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">Technology</button>
                            <button onclick="filterVideos('travel')" class="category-filter px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">Travel</button>
                            <button onclick="filterVideos('cooking')" class="category-filter px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">Cooking</button>
                        </div>
                    </div>
                    <!-- Video Grid -->
                    <div id="video-grid" class="grid grid-cols-1 gap-6 mt-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        <!-- Video Card 1 -->
                        <div class="video-card bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-xl" data-category="technology">
                            <div class="relative video-thumbnail-container cursor-pointer" onclick="playVideo('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4')">
                                <img src="https://placehold.co/400x225/a78bfa/ffffff?text=Tech" alt="Video Thumbnail" class="w-full h-40 object-cover">
                                <div class="play-icon-overlay absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity duration-300">
                                    <i class="fas fa-play text-white text-4xl"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 truncate">Getting Started with React Hooks</h3>
                                <p class="text-sm text-gray-500">15,203 views &bull; 12:45</p>
                                <div class="mt-4 flex items-center justify-between">
                                    <select class="text-sm border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="technology" selected>Technology</option>
                                        <option value="travel">Travel</option>
                                        <option value="cooking">Cooking</option>
                                    </select>
                                    <div class="text-gray-500">
                                        <a href="#" class="hover:text-indigo-600"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="ml-3 hover:text-red-600"><i class="fas fa-trash"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Video Card 2 -->
                        <div class="video-card bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-xl" data-category="travel">
                            <div class="relative video-thumbnail-container cursor-pointer" onclick="playVideo('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4')">
                                <img src="https://placehold.co/400x225/34d399/ffffff?text=Travel" alt="Video Thumbnail" class="w-full h-40 object-cover">
                                <div class="play-icon-overlay absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity duration-300">
                                    <i class="fas fa-play text-white text-4xl"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 truncate">Exploring the Swiss Alps</h3>
                                <p class="text-sm text-gray-500">8,940 views &bull; 08:22</p>
                                <div class="mt-4 flex items-center justify-between">
                                    <select class="text-sm border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="technology">Technology</option>
                                        <option value="travel" selected>Travel</option>
                                        <option value="cooking">Cooking</option>
                                    </select>
                                    <div class="text-gray-500">
                                        <a href="#" class="hover:text-indigo-600"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="ml-3 hover:text-red-600"><i class="fas fa-trash"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Video Card 3 -->
                        <div class="video-card bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-xl" data-category="cooking">
                            <div class="relative video-thumbnail-container cursor-pointer" onclick="playVideo('https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4')">
                                <img src="https://placehold.co/400x225/f87171/ffffff?text=Cooking" alt="Video Thumbnail" class="w-full h-40 object-cover">
                                <div class="play-icon-overlay absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity duration-300">
                                    <i class="fas fa-play text-white text-4xl"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 truncate">Perfect Pasta Carbonara</h3>
                                <p class="text-sm text-gray-500">25,109 views &bull; 05:30</p>
                                <div class="mt-4 flex items-center justify-between">
                                    <select class="text-sm border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="technology">Technology</option>
                                        <option value="travel">Travel</option>
                                        <option value="cooking" selected>Cooking</option>
                                    </select>
                                    <div class="text-gray-500">
                                        <a href="#" class="hover:text-indigo-600"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="ml-3 hover:text-red-600"><i class="fas fa-trash"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                         <!-- Add more video cards as needed -->
                    </div>
                </div>

                <!-- User Management Section -->
                <div id="users" class="hidden content-section">
                    <div class="flex items-center justify-between">
                        <h1 class="text-3xl font-bold text-gray-800">User Management</h1>
                        <button class="flex items-center px-4 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                            <i class="fas fa-user-plus mr-2"></i>
                            Add User
                        </button>
                    </div>
                     <div class="mt-8 bg-white rounded-lg shadow-md">
                         <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Joined</th>
                                        <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Sample User Row 1 -->
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img class="h-10 w-10 rounded-full" src="https://placehold.co/100x100/60a5fa/ffffff?text=JS" alt="">
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Jane Smith</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Editor</span></td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">June 15, 2025</td>
                                        <td class="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                                            <a href="#" class="text-indigo-600 hover:text-indigo-900"><i class="fas fa-edit"></i></a>
                                            <a href="#" class="ml-4 text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></a>
                                        </td>
                                    </tr>
                                    <!-- Sample User Row 2 -->
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img class="h-10 w-10 rounded-full" src="https://placehold.co/100x100/f472b6/ffffff?text=CD" alt="">
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Carlos Diaz</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Viewer</span></td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">May 02, 2025</td>
                                        <td class="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                                            <a href="#" class="text-indigo-600 hover:text-indigo-900"><i class="fas fa-edit"></i></a>
                                            <a href="#" class="ml-4 text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Categories Management Section -->
                <div id="categories" class="hidden content-section">
                     <h1 class="text-3xl font-bold text-gray-800">Categories</h1>
                     <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
                         <!-- Add Category Form -->
                         <div class="bg-white p-6 rounded-lg shadow-md">
                             <h2 class="text-xl font-semibold text-gray-800 mb-4">Add New Category</h2>
                             <form>
                                 <div>
                                     <label for="category-name" class="block text-sm font-medium text-gray-700">Category Name</label>
                                     <input type="text" id="category-name" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., Technology">
                                 </div>
                                 <div class="mt-4">
                                     <label for="category-desc" class="block text-sm font-medium text-gray-700">Description</label>
                                     <textarea id="category-desc" rows="3" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="A short description..."></textarea>
                                 </div>
                                 <div class="mt-6">
                                     <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                                         Add Category
                                     </button>
                                 </div>
                             </form>
                         </div>
                         <!-- Existing Categories List -->
                         <div class="bg-white p-6 rounded-lg shadow-md">
                             <h2 class="text-xl font-semibold text-gray-800 mb-4">Existing Categories</h2>
                             <ul class="divide-y divide-gray-200">
                                 <li class="py-3 flex justify-between items-center">
                                     <span class="text-gray-800 font-medium">Technology</span>
                                     <a href="#" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></a>
                                 </li>
                                 <li class="py-3 flex justify-between items-center">
                                     <span class="text-gray-800 font-medium">Travel</span>
                                     <a href="#" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></a>
                                 </li>
                                 <li class="py-3 flex justify-between items-center">
                                     <span class="text-gray-800 font-medium">Cooking</span>
                                     <a href="#" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></a>
                                 </li>
                                 <li class="py-3 flex justify-between items-center">
                                     <span class="text-gray-800 font-medium">DIY</span>
                                     <a href="#" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></a>
                                 </li>
                                  <li class="py-3 flex justify-between items-center">
                                     <span class="text-gray-800 font-medium">Education</span>
                                     <a href="#" class="text-red-500 hover:text-red-700"><i class="fas fa-trash"></i></a>
                                 </li>
                             </ul>
                         </div>
                     </div>
                </div>

                <!-- Settings Section -->
                <div id="settings" class="hidden content-section">
                    <h1 class="text-3xl font-bold text-gray-800">Settings</h1>
                     <div class="mt-8 max-w-2xl">
                        <div class="bg-white p-6 rounded-lg shadow-md">
                            <h2 class="text-xl font-semibold text-gray-800 mb-6 border-b pb-4">Profile Settings</h2>
                            <form>
                                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div>
                                        <label for="first-name" class="block text-sm font-medium text-gray-700">First Name</label>
                                        <input type="text" id="first-name" value="Admin" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="last-name" class="block text-sm font-medium text-gray-700">Last Name</label>
                                        <input type="text" id="last-name" value="User" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                        <input type="email" id="email" value="<EMAIL>" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    </div>
                                </div>
                                <div class="mt-6 pt-6 border-t">
                                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Change Password</h2>
                                    <div>
                                        <label for="current-password" class="block text-sm font-medium text-gray-700">Current Password</label>
                                        <input type="password" id="current-password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    </div>
                                    <div class="mt-4">
                                        <label for="new-password" class="block text-sm font-medium text-gray-700">New Password</label>
                                        <input type="password" id="new-password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    </div>
                                </div>
                                <div class="mt-6 text-right">
                                    <button type="submit" class="px-6 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">
                                        Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Video Player Modal -->
    <div id="video-player-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <!-- Overlay -->
        <div class="absolute inset-0 bg-black modal-overlay opacity-0" onclick="closeVideoPlayer()"></div>
        <!-- Modal Content -->
        <div class="z-10 w-full max-w-4xl mx-4 transform scale-95 modal-container">
            <div class="relative bg-black rounded-lg shadow-lg" style="padding-bottom: 56.25%; height: 0;">
                <video id="modal-video-player" class="absolute top-0 left-0 w-full h-full" controls autoplay></video>
                <button onclick="closeVideoPlayer()" class="absolute -top-3 -right-3 text-white bg-gray-800 rounded-full h-10 w-10 flex items-center justify-center hover:bg-red-600 transition-all focus:outline-none">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // JavaScript to handle sidebar and navigation
        const sidebar = document.getElementById('sidebar');
        const openSidebarBtn = document.getElementById('open-sidebar');
        const closeSidebarBtn = document.getElementById('close-sidebar');

        openSidebarBtn.addEventListener('click', () => {
            sidebar.classList.remove('-translate-x-full');
        });

        closeSidebarBtn.addEventListener('click', () => {
            sidebar.classList.add('-translate-x-full');
        });

        // Function to show/hide content sections
        function showSection(sectionId, element) {
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.add('hidden');
            });
            document.getElementById(sectionId).classList.remove('hidden');
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            element.classList.add('active');
            if (window.innerWidth < 768) {
                sidebar.classList.add('-translate-x-full');
            }
        }

        // --- Video Player Modal Logic ---
        const videoPlayerModal = document.getElementById('video-player-modal');
        const modalVideoPlayer = document.getElementById('modal-video-player');
        const modalOverlay = videoPlayerModal.querySelector('.modal-overlay');
        const modalContainer = videoPlayerModal.querySelector('.modal-container');

        function playVideo(videoSrc) {
            modalVideoPlayer.src = videoSrc;
            videoPlayerModal.classList.remove('hidden');
            // Trigger transitions
            setTimeout(() => {
                modalOverlay.classList.add('opacity-75');
                modalContainer.classList.remove('scale-95');
            }, 10);
        }

        function closeVideoPlayer() {
            modalVideoPlayer.pause();
            modalVideoPlayer.src = ""; // Clear src
            // Trigger closing transitions
            modalOverlay.classList.remove('opacity-75');
            modalContainer.classList.add('scale-95');
            setTimeout(() => {
                videoPlayerModal.classList.add('hidden');
            }, 300); // Wait for transition to finish
        }

        // --- Video Filtering Logic ---
        function filterVideos(category) {
            const videoCards = document.querySelectorAll('.video-card');
            const filterButtons = document.querySelectorAll('.category-filter');

            // Update active button style
            filterButtons.forEach(button => {
                if (button.innerText.toLowerCase() === category) {
                    button.classList.add('bg-indigo-500', 'text-white');
                    button.classList.remove('bg-white', 'text-gray-700');
                } else {
                    button.classList.remove('bg-indigo-500', 'text-white');
                    button.classList.add('bg-white', 'text-gray-700');
                }
            });

            // Show/hide video cards
            videoCards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.classList.remove('hidden');
                } else {
                    card.classList.add('hidden');
                }
            });
        }
    </script>
</body>
</html>
